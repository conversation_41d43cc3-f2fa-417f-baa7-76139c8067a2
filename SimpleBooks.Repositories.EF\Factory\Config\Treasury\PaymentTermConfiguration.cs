﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury
{
    public class PaymentTermConfiguration : IEntityTypeConfiguration<PaymentTermModel>
    {
        public void Configure(EntityTypeBuilder<PaymentTermModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.PaymentTermName).IsUnique();

            builder.HasDiscriminator<string>("Discriminator")
                .HasValue<PaymentTermModel>(nameof(PaymentTermModel))
                .HasValue<PaymentTermStandardModel>(nameof(PaymentTermStandardModel))
                .HasValue<PaymentTermDateDrivenModel>(nameof(PaymentTermDateDrivenModel));
        }
    }
}
