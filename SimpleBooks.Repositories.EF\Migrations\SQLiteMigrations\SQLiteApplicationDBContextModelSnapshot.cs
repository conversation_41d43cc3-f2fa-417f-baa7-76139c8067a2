﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SimpleBooks.Repositories.EF.Factory.Contexts;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    [DbContext(typeof(SQLiteApplicationDBContext))]
    partial class SQLiteApplicationDBContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.14");

            modelBuilder.Entity("SimpleBooks.Models.Model.BeneficiaryTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BeneficiaryTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.<PERSON>("Id");

                    b.ToT<PERSON>("BeneficiaryType");

                    b.<PERSON>(
                        new
                        {
                            Id = "01JTKF9QSYN1TR6DM<PERSON>Y0MZ3BY6",
                            BeneficiaryTypeName = "Vendor"
                        },
                        new
                        {
                            Id = "01JTKF9ZC7W32GQAFRSVP00D47",
                            BeneficiaryTypeName = "Customer"
                        },
                        new
                        {
                            Id = "01JTKFA29V6V59R8C3DYMX90M3",
                            BeneficiaryTypeName = "Employee"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.HR.EmployeeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeEMail")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmployeeIsRep")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeePhone")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeName")
                        .IsUnique();

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Employee");

                    b.HasData(
                        new
                        {
                            Id = "01JRK7Q6NDYC4AEEPQBWNX7V2G",
                            EmployeeEMail = "<EMAIL>",
                            EmployeeIsRep = false,
                            EmployeeName = "Admin",
                            EmployeePhone = "1234567890",
                            IsActive = true,
                            UserId = "01JRK7KJVTERSKVHZGGX4DQ4M0"
                        },
                        new
                        {
                            Id = "01JRK7QNHFMD61A648A9N09122",
                            EmployeeEMail = "<EMAIL>",
                            EmployeeIsRep = false,
                            EmployeeName = "User",
                            EmployeePhone = "1234567890",
                            IsActive = true,
                            UserId = "01JRK7KXPW5RY0FPRAPV06E9G9"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateOnly>("BillDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("BillDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("BillId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTypeId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BillId")
                        .IsUnique();

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorTypeId");

                    b.ToTable("Bill");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillReturnModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateOnly>("BillReturnDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("BillReturnDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("BillReturnId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTypeId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BillReturnId")
                        .IsUnique();

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorTypeId");

                    b.ToTable("BillReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.PurchaseOrderLineModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductUnitId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PurchaseOrderId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitQtyRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUnitId");

                    b.HasIndex("PurchaseOrderId");

                    b.ToTable("PurchaseOrderLine");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.PurchaseOrderModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.Property<DateOnly>("PurchaseOrderDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("PurchaseOrderDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("PurchaseOrderId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTypeId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("PurchaseOrderId")
                        .IsUnique();

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorTypeId");

                    b.ToTable("PurchaseOrder");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTaxCardNumber")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTypeId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("VendorName")
                        .IsUnique();

                    b.HasIndex("VendorTaxCardNumber")
                        .IsUnique();

                    b.HasIndex("VendorTypeId");

                    b.ToTable("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("VendorTypeName")
                        .IsUnique();

                    b.ToTable("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerRepId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerTaxCardNumber")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerTypeId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerName")
                        .IsUnique();

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTaxCardNumber")
                        .IsUnique();

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("PaymentTermId");

                    b.ToTable("Customer");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerTypeName")
                        .IsUnique();

                    b.ToTable("CustomerType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerRepId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerTypeId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateOnly>("InvoiceDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("InvoiceDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("InvoiceId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("InvoiceId")
                        .IsUnique();

                    b.HasIndex("PaymentTermId");

                    b.ToTable("Invoice");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerRepId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerTypeId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateOnly>("InvoiceReturnDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("InvoiceReturnDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("InvoiceReturnId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("InvoiceReturnId")
                        .IsUnique();

                    b.HasIndex("PaymentTermId");

                    b.ToTable("InvoiceReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.SalesOrderLineModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductUnitId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("SalesOrderId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitQtyRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUnitId");

                    b.HasIndex("SalesOrderId");

                    b.ToTable("SalesOrderLine");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.SalesOrderModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerRepId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerTypeId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermId")
                        .HasColumnType("TEXT");

                    b.Property<DateOnly>("SalesOrderDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("SalesOrderDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("SalesOrderId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("SalesOrderId")
                        .IsUnique();

                    b.ToTable("SalesOrder");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_ar")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_en")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TaxTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("TaxTypeId");

                    b.ToTable("TaxSubType");

                    b.HasData(
                        new
                        {
                            Id = "01JRN6KEMJHZ2R4W9AXTZA6766",
                            Code = "V001",
                            Desc_ar = "تصدير للخارج",
                            Desc_en = "Export",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMJYW8C4QAVZH5MFST5",
                            Code = "V002",
                            Desc_ar = "تصدير مناطق حرة وأخرى",
                            Desc_en = "Export to free areas and other areas",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMJDGQ75J7RBKNTSTQF",
                            Code = "V003",
                            Desc_ar = "سلعة أو خدمة معفاة",
                            Desc_en = "Exempted good or service",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMJN95XY34QFPWNEH0S",
                            Code = "V004",
                            Desc_ar = "سلعة أو خدمة غير خاضعة للضريبة",
                            Desc_en = "A non-taxable good or service",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMJ3NA115CBHHXNJMNY",
                            Code = "V005",
                            Desc_ar = "إعفاءات دبلوماسين والقنصليات والسفارات",
                            Desc_en = "Exemptions for diplomats, consulates and embassies",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMJ1YSCTA2H6T6SED3P",
                            Code = "V006",
                            Desc_ar = "إعفاءات الدفاع والأمن القومى",
                            Desc_en = "Defence and National security Exemptions",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMKQRZFEB9ETRAP0VQZ",
                            Code = "V007",
                            Desc_ar = "إعفاءات اتفاقيات",
                            Desc_en = "Agreements exemptions",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMKVC7SSTYYN08KHRAP",
                            Code = "V008",
                            Desc_ar = "إعفاءات خاصة و أخرى",
                            Desc_en = "Special Exemptios and other reasons",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMK6V5HH0W4PNZRZTKV",
                            Code = "V009",
                            Desc_ar = "سلع عامة",
                            Desc_en = "General Item sales",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMKSXCH3YEM40NV096J",
                            Code = "V010",
                            Desc_ar = "نسب ضريبة أخرى",
                            Desc_en = "Other Rates",
                            TaxTypeId = "01JRN6KEMGHX79ZW8H9GPDEF72"
                        },
                        new
                        {
                            Id = "01JRN6KEMKTA96NHMZAGVDY0E6",
                            Code = "Tbl01",
                            Desc_ar = "ضريبه الجدول (نسبيه)",
                            Desc_en = "Table tax (percentage)",
                            TaxTypeId = "01JRN6KEMHT2B8G7NZVX6J0B7W"
                        },
                        new
                        {
                            Id = "01JRN6KEMKCS6EH5C8G1KT33FC",
                            Code = "Tbl02",
                            Desc_ar = "ضريبه الجدول (النوعية)",
                            Desc_en = "Table tax (Fixed Amount)",
                            TaxTypeId = "01JRN6KEMHQ1MEF9FBCDT2DA5S"
                        },
                        new
                        {
                            Id = "01JRN6KEMK16XJXNQC94B8P1P3",
                            Code = "W001",
                            Desc_ar = "المقاولات",
                            Desc_en = "Contracting",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKWJB4PXVWBX0139BV",
                            Code = "W002",
                            Desc_ar = "التوريدات",
                            Desc_en = "Supplies",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKBWWK3D8TX38V7ZPC",
                            Code = "W003",
                            Desc_ar = "المشتريات",
                            Desc_en = "Purachases",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKKTWSK3JFV867PDGR",
                            Code = "W004",
                            Desc_ar = "الخدمات",
                            Desc_en = "Services",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKMCDVD7FRD6BVANG4",
                            Code = "W005",
                            Desc_ar = "المبالغالتي تدفعها الجميعات التعاونية للنقل بالسيارات لاعضائها",
                            Desc_en = "Sumspaid by the cooperative societies for car transportation to their members",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKVBZ51PD3AN1PA1ZA",
                            Code = "W006",
                            Desc_ar = "الوكالةبالعمولة والسمسرة",
                            Desc_en = "Commissionagency & brokerage",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKXYYQP6SKY6ZSA30E",
                            Code = "W007",
                            Desc_ar = "الخصوماتوالمنح والحوافز الاستثنائية ةالاضافية التي تمنحها شركات الدخان والاسمنت ",
                            Desc_en = "Discounts& grants & additional exceptional incentives granted by smoke &cement companies",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMKD21BYKY0WSHCVT4G",
                            Code = "W008",
                            Desc_ar = "جميعالخصومات والمنح والعمولات  التيتمنحها  شركات البترول والاتصالات ...وغيرها من الشركات المخاطبة بنظام الخصم",
                            Desc_en = "Alldiscounts & grants & commissions granted by petroleum &telecommunications & other companies",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMK5Y5624NNFY3CM2X",
                            Code = "W009",
                            Desc_ar = "مساندة دعم الصادرات التي يمنحها صندوق تنمية الصادرات ",
                            Desc_en = "Supporting export subsidies",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMT81BZZFVSR758YVM",
                            Code = "W010",
                            Desc_ar = "اتعاب مهنية",
                            Desc_en = "Professional fees",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMQQANZQHGBAQZ74ZV",
                            Code = "W011",
                            Desc_ar = "العمولة والسمسرة _م_57",
                            Desc_en = "Commission & brokerage _A_57",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMM5NDW2Q2BVR60JV3H",
                            Code = "W012",
                            Desc_ar = "تحصيل المستشفيات من الاطباء",
                            Desc_en = "Hospitals collecting from doctors",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMHDGXE64KP2PH6EE4",
                            Code = "W013",
                            Desc_ar = "الاتاوات",
                            Desc_en = "Royalties",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMPGBKG2JDHFYWS9FH",
                            Code = "W014",
                            Desc_ar = "تخليص جمركي ",
                            Desc_en = "Customs clearance",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMD545W0VC5HPP6CMH",
                            Code = "W015",
                            Desc_ar = "أعفاء",
                            Desc_en = "Exemption",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMA4AMVGMZQHMK19WF",
                            Code = "W016",
                            Desc_ar = "دفعات مقدمه",
                            Desc_en = "advance payments",
                            TaxTypeId = "01JRN6KEMHMNRPK8CTMAZ3XQTK"
                        },
                        new
                        {
                            Id = "01JRN6KEMMYAWRRQTZG75XKZ68",
                            Code = "ST01",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            TaxTypeId = "01JRN6KEMHVHVT0JR971YVPJ92"
                        },
                        new
                        {
                            Id = "01JRN6KEMM43A145PBRYW0A8CH",
                            Code = "ST02",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت)",
                            Desc_en = "Stamping Tax (amount)",
                            TaxTypeId = "01JRN6KEMJ1FATNAQ3540BR73H"
                        },
                        new
                        {
                            Id = "01JRN6KEMMKPEKR0H72HVAXK3R",
                            Code = "Ent01",
                            Desc_ar = "ضريبة الملاهى (نسبة)",
                            Desc_en = "Entertainment tax (rate)",
                            TaxTypeId = "01JRN6KEMJCN4J6XCNJGBCSH9D"
                        },
                        new
                        {
                            Id = "01JRN6KEMM6Y65XSCXD951QW26",
                            Code = "Ent02",
                            Desc_ar = "ضريبة الملاهى (قطعية)",
                            Desc_en = "Entertainment tax (amount)",
                            TaxTypeId = "01JRN6KEMJCN4J6XCNJGBCSH9D"
                        },
                        new
                        {
                            Id = "01JRN6KEMM67M7BNBJ9FAPR43J",
                            Code = "RD01",
                            Desc_ar = "رسم تنميه الموارد (نسبة)",
                            Desc_en = "Resource development fee (rate)",
                            TaxTypeId = "01JRN6KEMJ7CWC2EZBRBKD2RFJ"
                        },
                        new
                        {
                            Id = "01JRN6KEMM3ABBYMMX9GWY5T60",
                            Code = "RD02",
                            Desc_ar = "رسم تنميه الموارد (قطعية)",
                            Desc_en = "Resource development fee (amount)",
                            TaxTypeId = "01JRN6KEMJ7CWC2EZBRBKD2RFJ"
                        },
                        new
                        {
                            Id = "01JRN6KEMMFYB9TP9SARKH30HY",
                            Code = "SC01",
                            Desc_ar = "رسم خدمة (نسبة)",
                            Desc_en = "Service charges (rate)",
                            TaxTypeId = "01JRN6KEMJHNG5Q0R5AMHSBV85"
                        },
                        new
                        {
                            Id = "01JRN6KEMMAP568B5A5CQQ78K4",
                            Code = "SC02",
                            Desc_ar = "رسم خدمة (قطعية)",
                            Desc_en = "Service charges (amount)",
                            TaxTypeId = "01JRN6KEMJHNG5Q0R5AMHSBV85"
                        },
                        new
                        {
                            Id = "01JRN6KEMNDB4GR4N4NMH112ES",
                            Code = "Mn01",
                            Desc_ar = "رسم المحليات (نسبة)",
                            Desc_en = "Municipality Fees (rate)",
                            TaxTypeId = "01JRN6KEMJ1EXMTWFQQ8BYP7RS"
                        },
                        new
                        {
                            Id = "01JRN6R5RVF7YCQ9H08A9ATJYH",
                            Code = "Mn02",
                            Desc_ar = "رسم المحليات (قطعية)",
                            Desc_en = "Municipality Fees (amount)",
                            TaxTypeId = "01JRN6KEMJ1EXMTWFQQ8BYP7RS"
                        },
                        new
                        {
                            Id = "01JRN6R5RVRQQGXHVN4RRAM98G",
                            Code = "MI01",
                            Desc_ar = "رسم التامين الصحى (نسبة)",
                            Desc_en = "Medical insurance fee (rate)",
                            TaxTypeId = "01JRN6KEMJ36AZH3TSZM1QXXQ2"
                        },
                        new
                        {
                            Id = "01JRN6R5RVAZ79A391959E97ZY",
                            Code = "MI02",
                            Desc_ar = "رسم التامين الصحى (قطعية)",
                            Desc_en = "Medical insurance fee (amount)",
                            TaxTypeId = "01JRN6KEMJ36AZH3TSZM1QXXQ2"
                        },
                        new
                        {
                            Id = "01JRN6R5RVY6G76MCFE14P5F9X",
                            Code = "OF01",
                            Desc_ar = "رسوم أخرى (نسبة)",
                            Desc_en = "Other fees (rate)",
                            TaxTypeId = "01JRN6KEMJ4X5C3NYGDDSZEGEM"
                        },
                        new
                        {
                            Id = "01JRN6R5RVRMKR8A43X519YQ7Y",
                            Code = "OF02",
                            Desc_ar = "رسوم أخرى (قطعية)",
                            Desc_en = "Other fees (amount)",
                            TaxTypeId = "01JRN6KEMJ4X5C3NYGDDSZEGEM"
                        },
                        new
                        {
                            Id = "01JRN6R5RV78GJ7RVTYF1R71PD",
                            Code = "ST03",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            TaxTypeId = "01JRN6R5RWY935F174XQM19Y1T"
                        },
                        new
                        {
                            Id = "01JRN6R5RVXDKMWNBE94H65ES3",
                            Code = "ST04",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت)",
                            Desc_en = "Stamping Tax (amount)",
                            TaxTypeId = "01JRN6R5RW3G72VRJ4Q2CA2JTV"
                        },
                        new
                        {
                            Id = "01JRN6R5RWR5ZT5NTWW5CNCKTT",
                            Code = "Ent03",
                            Desc_ar = "ضريبة الملاهى (نسبة)",
                            Desc_en = "Entertainment tax (rate)",
                            TaxTypeId = "01JRN6R5RWG7KPJMKBDAHZVHDH"
                        },
                        new
                        {
                            Id = "01JRN6R5RWGVAV02Q9T02G4CH6",
                            Code = "Ent04",
                            Desc_ar = "ضريبة الملاهى (قطعية)",
                            Desc_en = "Entertainment tax (amount)",
                            TaxTypeId = "01JRN6R5RWG7KPJMKBDAHZVHDH"
                        },
                        new
                        {
                            Id = "01JRN6R5RWXJNBXBPHW42EKGPV",
                            Code = "RD03",
                            Desc_ar = "رسم تنميه الموارد (نسبة)",
                            Desc_en = "Resource development fee (rate)",
                            TaxTypeId = "01JRN6R5RW7PD5HBF3563V3JN7"
                        },
                        new
                        {
                            Id = "01JRN6R5RWRA4Q22AGM4CR283T",
                            Code = "RD04",
                            Desc_ar = "رسم تنميه الموارد (قطعية)",
                            Desc_en = "Resource development fee (amount)",
                            TaxTypeId = "01JRN6R5RW7PD5HBF3563V3JN7"
                        },
                        new
                        {
                            Id = "01JRN6R5RWR4NE3D53E1T1MYPS",
                            Code = "SC03",
                            Desc_ar = "رسم خدمة (نسبة)",
                            Desc_en = "Service charges (rate)",
                            TaxTypeId = "01JRN6R5RWFYWMR74SJCX2G4XN"
                        },
                        new
                        {
                            Id = "01JRN6R5RW4NEPJXPJT8TF4SV2",
                            Code = "SC04",
                            Desc_ar = "رسم خدمة (قطعية)",
                            Desc_en = "Service charges (amount)",
                            TaxTypeId = "01JRN6R5RWFYWMR74SJCX2G4XN"
                        },
                        new
                        {
                            Id = "01JRN6R5RW1CAGGTV477HF59AD",
                            Code = "Mn03",
                            Desc_ar = "رسم المحليات (نسبة)",
                            Desc_en = "Municipality Fees (rate)",
                            TaxTypeId = "01JRN6R5RWB9NB0XFSY0G9VG4S"
                        },
                        new
                        {
                            Id = "01JRN6R5RW49QW3MNMS8XG2T9M",
                            Code = "Mn04",
                            Desc_ar = "رسم المحليات (قطعية)",
                            Desc_en = "Municipality Fees (amount)",
                            TaxTypeId = "01JRN6R5RWB9NB0XFSY0G9VG4S"
                        },
                        new
                        {
                            Id = "01JRN6R5RWKSP3T79AGWKFSJ4E",
                            Code = "MI03",
                            Desc_ar = "رسم التامين الصحى (نسبة)",
                            Desc_en = "Medical insurance fee (rate)",
                            TaxTypeId = "01JRN6R5RWET9R49NR7QTZA801"
                        },
                        new
                        {
                            Id = "01JRN6R5RW4TE80A5E1DPND9SE",
                            Code = "MI04",
                            Desc_ar = "رسم التامين الصحى (قطعية)",
                            Desc_en = "Medical insurance fee (amount)",
                            TaxTypeId = "01JRN6R5RWET9R49NR7QTZA801"
                        },
                        new
                        {
                            Id = "01JRN6R5RWK548QE6RKDV8NKXZ",
                            Code = "OF03",
                            Desc_ar = "رسوم أخرى (نسبة)",
                            Desc_en = "Other fees (rate)",
                            TaxTypeId = "01JRN6R5RWNEB4PMWHKDY8KEEW"
                        },
                        new
                        {
                            Id = "01JRN6R5RW16W2TCJTEXPB22FH",
                            Code = "OF04",
                            Desc_ar = "رسوم أخرى (قطعية)",
                            Desc_en = "Other fees (amount)",
                            TaxTypeId = "01JRN6R5RWNEB4PMWHKDY8KEEW"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_ar")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_en")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsAddition")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("TaxType");

                    b.HasData(
                        new
                        {
                            Id = "01JRN6KEMGHX79ZW8H9GPDEF72",
                            Code = "T1",
                            Desc_ar = "ضريبه القيمه المضافه",
                            Desc_en = "Value added tax",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMHT2B8G7NZVX6J0B7W",
                            Code = "T2",
                            Desc_ar = "ضريبه الجدول (نسبيه)",
                            Desc_en = "Table tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMHQ1MEF9FBCDT2DA5S",
                            Code = "T3",
                            Desc_ar = "ضريبه الجدول (قطعيه)",
                            Desc_en = "Table tax (Fixed Amount)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMHMNRPK8CTMAZ3XQTK",
                            Code = "T4",
                            Desc_ar = "الخصم تحت حساب الضريبه",
                            Desc_en = "Withholding tax (WHT)",
                            IsAddition = false
                        },
                        new
                        {
                            Id = "01JRN6KEMHVHVT0JR971YVPJ92",
                            Code = "T5",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJ1FATNAQ3540BR73H",
                            Code = "T6",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت )",
                            Desc_en = "Stamping Tax (amount)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJCN4J6XCNJGBCSH9D",
                            Code = "T7",
                            Desc_ar = "ضريبة الملاهى",
                            Desc_en = "Entertainment tax",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJ7CWC2EZBRBKD2RFJ",
                            Code = "T8",
                            Desc_ar = "رسم تنميه الموارد",
                            Desc_en = "Resource development fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJHNG5Q0R5AMHSBV85",
                            Code = "T9",
                            Desc_ar = "رسم خدمة",
                            Desc_en = "Table tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJ1EXMTWFQQ8BYP7RS",
                            Code = "T10",
                            Desc_ar = "رسم المحليات",
                            Desc_en = "Municipality Fees",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJ36AZH3TSZM1QXXQ2",
                            Code = "T11",
                            Desc_ar = "رسم التامين الصحى",
                            Desc_en = "Medical insurance fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6KEMJ4X5C3NYGDDSZEGEM",
                            Code = "T12",
                            Desc_ar = "رسوم أخري",
                            Desc_en = "Other fees",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RWY935F174XQM19Y1T",
                            Code = "T13",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RW3G72VRJ4Q2CA2JTV",
                            Code = "T14",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت )",
                            Desc_en = "Stamping Tax (amount)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RWG7KPJMKBDAHZVHDH",
                            Code = "T15",
                            Desc_ar = "ضريبة الملاهى",
                            Desc_en = "Entertainment tax",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RW7PD5HBF3563V3JN7",
                            Code = "T16",
                            Desc_ar = "رسم تنميه الموارد",
                            Desc_en = "Resource development fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RWFYWMR74SJCX2G4XN",
                            Code = "T17",
                            Desc_ar = "رسم خدمة",
                            Desc_en = "Table tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RWB9NB0XFSY0G9VG4S",
                            Code = "T18",
                            Desc_ar = "رسم المحليات",
                            Desc_en = "Municipality Fees",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RWET9R49NR7QTZA801",
                            Code = "T19",
                            Desc_ar = "رسم التامين الصحى",
                            Desc_en = "Medical insurance fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = "01JRN6R5RWNEB4PMWHKDY8KEEW",
                            Code = "T20",
                            Desc_ar = "رسوم أخرى",
                            Desc_en = "Other fees",
                            IsAddition = true
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.TransactionTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("TransactionTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("TransactionType");

                    b.HasData(
                        new
                        {
                            Id = "01JRKA5XSNHEQWKTR0NCJWYC6W",
                            TransactionTypeName = "Bill"
                        },
                        new
                        {
                            Id = "01JRKA5XSQ45QDM1C7H1TME01A",
                            TransactionTypeName = "Bill Return"
                        },
                        new
                        {
                            Id = "01JRKA5XSQSFE4H3R3M177EDN5",
                            TransactionTypeName = "Invoice"
                        },
                        new
                        {
                            Id = "01JRKA5XSQGDQ36JWP7BFAE06R",
                            TransactionTypeName = "Invoice Return"
                        },
                        new
                        {
                            Id = "01JRKA5XSQ95F5J1D2DBHHM375",
                            TransactionTypeName = "Inventory Adjustment"
                        },
                        new
                        {
                            Id = "01JRKA5XSR7RW0JNTPY9X6JPBV",
                            TransactionTypeName = "Inventory Transfer"
                        },
                        new
                        {
                            Id = "01JRKA5XSRKZCHG3CMFM9ZPRN7",
                            TransactionTypeName = "Inventory Opening Balance"
                        },
                        new
                        {
                            Id = "01JRKA5XSRQHRCBY9A06M2J1RQ",
                            TransactionTypeName = "SalesOrder"
                        },
                        new
                        {
                            Id = "01JZBSNG4F3P7EYRA6T1JY5HPC",
                            TransactionTypeName = "Cash In"
                        },
                        new
                        {
                            Id = "01JZBSNQWAHRA4CK6TKK9SVY43",
                            TransactionTypeName = "Cash Out"
                        },
                        new
                        {
                            Id = "01JZBSP3PFVH37SZ8D5DEQF304",
                            TransactionTypeName = "Check In"
                        },
                        new
                        {
                            Id = "01JZBSP7B64B1GAKF49A3D8M9S",
                            TransactionTypeName = "Check Out"
                        },
                        new
                        {
                            Id = "01JZBSPCXBX6NRP69E2X96YSW5",
                            TransactionTypeName = "Bank Transfer In"
                        },
                        new
                        {
                            Id = "01JZBSPK8EG8Z9AVT9K2KQ3XXC",
                            TransactionTypeName = "Bank Transfer Out"
                        },
                        new
                        {
                            Id = "01JZBSNVFRKWDYN58VC8Y5CPXM",
                            TransactionTypeName = "Bank In"
                        },
                        new
                        {
                            Id = "01JZBSP0S7B0C5T3YXWJ2WSGPP",
                            TransactionTypeName = "Bank Out"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountCurrency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountIBAN")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountSwiftCode")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.ToTable("BankAccount");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankIdentifier")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BankName")
                        .IsUnique();

                    b.ToTable("Bank");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BankId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BeneficiaryTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerId")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("TVID")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.Property<string>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorId")
                        .HasColumnType("TEXT");

                    b.Property<int>("VoucherSerial")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BankId");

                    b.HasIndex("BeneficiaryTypeId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("VendorId");

                    b.ToTable("BankTransferTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ClearDate")
                        .HasColumnType("Date");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CheckClear");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CollectionDate")
                        .HasColumnType("Date");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CheckCollection");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BankId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DepositDate")
                        .HasColumnType("Date");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BankId");

                    b.ToTable("CheckDeposit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("RejectDate")
                        .HasColumnType("Date");

                    b.Property<string>("RejectReason")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CheckReject");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultLocationId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ReturnDate")
                        .HasColumnType("Date");

                    b.HasKey("Id");

                    b.HasIndex("CheckVaultId");

                    b.HasIndex("CheckVaultLocationId");

                    b.ToTable("CheckReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusHistoryModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckClearId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckCollectionId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckDepositId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckRejectId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckReturnId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckStatusFromId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckStatusToId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckTreasuryVoucherId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.HasKey("Id");

                    b.HasIndex("CheckClearId");

                    b.HasIndex("CheckCollectionId");

                    b.HasIndex("CheckDepositId");

                    b.HasIndex("CheckRejectId");

                    b.HasIndex("CheckReturnId");

                    b.HasIndex("CheckStatusFromId");

                    b.HasIndex("CheckStatusToId");

                    b.HasIndex("CheckTreasuryVoucherId");

                    b.ToTable("CheckStatusHistory");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckStatusName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CheckStatus");

                    b.HasData(
                        new
                        {
                            Id = "01JZXN7EBKTG5YX238JNT92KFD",
                            CheckStatusName = "Unknown",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01JZBRJWHRNQTHR1MJ693B2STZ",
                            CheckStatusName = "Issued",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01JZBRK6P4GNTX4097YAKT3ZRZ",
                            CheckStatusName = "Cleared",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01JZBRK149KQWD0TJ971CQ5WC1",
                            CheckStatusName = "Received",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01JZBRK3ZGQPTB0S7RNN8QC8EA",
                            CheckStatusName = "Deposited",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01K0J1KKN1YZMJYQ86F7C4JMS4",
                            CheckStatusName = "Collected",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01JZBRKD187QRR8QSB7SWJQ4TM",
                            CheckStatusName = "Rejected",
                            IsActive = true
                        },
                        new
                        {
                            Id = "01JZBRKA75CZZW5P4ARW47NY4P",
                            CheckStatusName = "Returned",
                            IsActive = true
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountId")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankId")
                        .HasColumnType("TEXT");

                    b.Property<string>("BearerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BeneficiaryTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckClearId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckCollectionId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckDepositId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckRejectId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckReturnId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckStatusId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultLocationId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("Date");

                    b.Property<string>("EmployeeId")
                        .HasColumnType("TEXT");

                    b.Property<string>("IssuerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("TVID")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.Property<string>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorId")
                        .HasColumnType("TEXT");

                    b.Property<int>("VoucherSerial")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BankId");

                    b.HasIndex("BeneficiaryTypeId");

                    b.HasIndex("CheckClearId");

                    b.HasIndex("CheckCollectionId");

                    b.HasIndex("CheckDepositId");

                    b.HasIndex("CheckRejectId");

                    b.HasIndex("CheckReturnId");

                    b.HasIndex("CheckStatusId");

                    b.HasIndex("CheckVaultId");

                    b.HasIndex("CheckVaultLocationId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("VendorId");

                    b.ToTable("CheckTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultLocationCurrency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultLocationNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CheckVaultId");

                    b.ToTable("CheckVaultLocation");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CheckVaultName")
                        .IsUnique();

                    b.ToTable("CheckVault");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("BeneficiaryTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerId")
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerLocationId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("TVID")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.Property<string>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorId")
                        .HasColumnType("TEXT");

                    b.Property<int>("VoucherSerial")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BeneficiaryTypeId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DrawerId");

                    b.HasIndex("DrawerLocationId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("VendorId");

                    b.ToTable("CashTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerLocationCurrency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerLocationNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DrawerId");

                    b.ToTable("DrawerLocation");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DrawerName")
                        .IsUnique();

                    b.ToTable("Drawer");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.ExpensesModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("ExpensesDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("ExpensesName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ExpensesName")
                        .IsUnique();

                    b.ToTable("Expenses");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<int>("DiscountPercentage")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTermName")
                        .IsUnique();

                    b.ToTable("PaymentTerm");

                    b.HasDiscriminator().HasValue("PaymentTermModel");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.TreasuryLineModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("BankTransferTreasuryVoucherId")
                        .HasColumnType("TEXT");

                    b.Property<string>("BillId")
                        .HasColumnType("TEXT");

                    b.Property<string>("BillReturnId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CashTreasuryVoucherId")
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckTreasuryVoucherId")
                        .HasColumnType("TEXT");

                    b.Property<string>("ExpensesId")
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceId")
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceReturnId")
                        .HasColumnType("TEXT");

                    b.Property<string>("TreasuryLineTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BankTransferTreasuryVoucherId");

                    b.HasIndex("BillId");

                    b.HasIndex("BillReturnId");

                    b.HasIndex("CashTreasuryVoucherId");

                    b.HasIndex("CheckTreasuryVoucherId");

                    b.HasIndex("ExpensesId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("InvoiceReturnId");

                    b.ToTable("TreasuryLine");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.AuditModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("AffectedColumns")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("NewValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OldValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PrimaryKey")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.RefreshTokenModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpiresOn")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("RevokedOn")
                        .HasColumnType("TEXT");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileDetailsModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<bool>("CanAdd")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanOpen")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanPrint")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanShow")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ScreenId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ScreensAccessProfileId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ScreensAccessProfileId");

                    b.ToTable("ScreensAccessProfileDetails");

                    b.HasData(
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24ANT1",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDEWAV2Y5X4PHYM77X7N0K",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CC1P",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDF06EDYCFBKYBQ6HJA9B0",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CCT6",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDF3FVVBEPAZ4P6JW9A7VT",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CDSG",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDF701Z5JTMT806618B3F0",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CGAQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFAWMWF4WM11MVFS4RE57",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CH9G",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFE0NWTYZ240ACVMXEMDF",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CJ25",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFHEBWM6CD8QSKDZFTK2W",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CKJT",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFNZ5E91ZMTK93SVBZ7P4",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CMJS",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFRY8QR4PRX21SC9J23WG",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CNSQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFW74XJS7C2D605TNCD26",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8N95MDJQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVERZ6WQNV4MFHE749ZR5N8M",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8N95MGJ7",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVERZBGWA4EGRR50MH56ZV78",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24CPHH",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFZ1YB6QRVPSPPYKNTRFK",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24ED1P",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDG46MYREZC2FR685SVJ0Q",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24EDSJ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDG721H4X37Q0CKJDWVYDM",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24EEAA",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDG9JYD4KDHDRBP6226K36",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24EH2B",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDGDKH8N0DW4PVKW4640B0",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24EHTH",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDGGQ7TT2MWJVPXYYN8TTC",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24EJTJ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDGKRWHXCBWFA4DG8N2V38",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GDHR",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDH685AAYHRWX94ZNRT9QB",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GGAQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHAWK35FMS58RM8VVWWXZ",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GH23",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHDCFYMFHJ9K9YAV6F81Z",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GJ2R",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHHXK37EZSXTXV8EX8BF5",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GKA7",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHMGK13EGC7FRY6Q42T8C",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GM9K",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHQ3D35EKKWAFFZ85Z97Q",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GN1P",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHT620EY2GA969M52PAXF",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24GP2H",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHXQAK960655EDA6E8NKJ",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24MC9J",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJ12X03EPSWZR4VJGD2XA",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24MD2J",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJ4RQ2WASRZWEW0XKDG8Z",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24ME2S",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJ8YMZWNS14YXFA6FTTMC",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24MGJK",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJBSY2SXXEGTHSBVNTAZM",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0064R32JJP8H24MHHH",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJF1JSH5D1FB7JHPAY372",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24ANT1",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDEWAV2Y5X4PHYM77X7N0K",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CC1P",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDF06EDYCFBKYBQ6HJA9B0",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CCT6",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDF3FVVBEPAZ4P6JW9A7VT",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CDSG",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDF701Z5JTMT806618B3F0",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CGAQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFAWMWF4WM11MVFS4RE57",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CH9G",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFE0NWTYZ240ACVMXEMDF",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CJ25",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFHEBWM6CD8QSKDZFTK2W",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CKJT",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFNZ5E91ZMTK93SVBZ7P4",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CMJS",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFRY8QR4PRX21SC9J23WG",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CNSQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFW74XJS7C2D605TNCD26",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8N95MDJQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVERZ6WQNV4MFHE749ZR5N8M",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8N95MGJ7",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVERZBGWA4EGRR50MH56ZV78",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24CPHH",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDFZ1YB6QRVPSPPYKNTRFK",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24ED1P",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDG46MYREZC2FR685SVJ0Q",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24EDSJ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDG721H4X37Q0CKJDWVYDM",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24EEAA",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDG9JYD4KDHDRBP6226K36",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24EH2B",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDGDKH8N0DW4PVKW4640B0",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24EHTH",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDGGQ7TT2MWJVPXYYN8TTC",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24EJTJ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDGKRWHXCBWFA4DG8N2V38",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GDHR",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDH685AAYHRWX94ZNRT9QB",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GGAQ",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHAWK35FMS58RM8VVWWXZ",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GH23",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHDCFYMFHJ9K9YAV6F81Z",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GJ2R",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHHXK37EZSXTXV8EX8BF5",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GKA7",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHMGK13EGC7FRY6Q42T8C",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GM9K",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHQ3D35EKKWAFFZ85Z97Q",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GN1P",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHT620EY2GA969M52PAXF",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24GP2H",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDHXQAK960655EDA6E8NKJ",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24MC9J",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJ12X03EPSWZR4VJGD2XA",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24MD2J",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJ4RQ2WASRZWEW0XKDG8Z",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24ME2S",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJ8YMZWNS14YXFA6FTTMC",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24MGJK",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJBSY2SXXEGTHSBVNTAZM",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        },
                        new
                        {
                            Id = "67FKPX6A0068R32JJP8H24MHHH",
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = "01JVDDJF1JSH5D1FB7JHPAY372",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("ScreensAccessProfileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserModelId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserModelId");

                    b.ToTable("ScreensAccessProfile");

                    b.HasData(
                        new
                        {
                            Id = "01JVDCZD2ST67FD5DFX2KECWKR",
                            IsActive = true,
                            ScreensAccessProfileName = "Admin Profile"
                        },
                        new
                        {
                            Id = "01JVDCZHG4FZWV2XFZ4EEYJN3Z",
                            IsActive = true,
                            ScreensAccessProfileName = "User Profile"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.SettingModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Setting");

                    b.HasData(
                        new
                        {
                            Id = "01JM898XB2RH67PPFANBQV0KRG",
                            IsActive = true,
                            UserId = "01JRK7KJVTERSKVHZGGX4DQ4M0"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.UserModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("ScreensAccessProfileId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("SettingId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ScreensAccessProfileId");

                    b.HasIndex("UserName")
                        .IsUnique();

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = "01JRK7KJVTERSKVHZGGX4DQ4M0",
                            EmployeeId = "01JRK7Q6NDYC4AEEPQBWNX7V2G",
                            IsActive = true,
                            Password = "123456",
                            ScreensAccessProfileId = "01JVDCZD2ST67FD5DFX2KECWKR",
                            SettingId = "00000000000000000000000000",
                            UserName = "Admin",
                            UserTypeId = "01JPBH5VCQFNHEEFRPAN170618"
                        },
                        new
                        {
                            Id = "01JRK7KXPW5RY0FPRAPV06E9G9",
                            EmployeeId = "01JRK7QNHFMD61A648A9N09122",
                            IsActive = true,
                            Password = "123456",
                            ScreensAccessProfileId = "01JVDCZHG4FZWV2XFZ4EEYJN3Z",
                            SettingId = "00000000000000000000000000",
                            UserName = "User",
                            UserTypeId = "01JPBH694D11N4XRP18J9HWQWX"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BillId")
                        .HasColumnType("TEXT");

                    b.Property<string>("BillReturnId")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("CostAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("CostPrice")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceId")
                        .HasColumnType("TEXT");

                    b.Property<string>("InvoiceReturnId")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("NetAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductUnitId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PurchaseOrderId")
                        .HasColumnType("TEXT");

                    b.Property<string>("PurchaseOrderLineId")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SalesAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("SalesOrderId")
                        .HasColumnType("TEXT");

                    b.Property<string>("SalesOrderLineId")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SalesPrice")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("StoreId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("UnitQtyRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BillId");

                    b.HasIndex("BillReturnId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("InvoiceReturnId");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUnitId");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("SalesOrderId");

                    b.HasIndex("SalesOrderLineId");

                    b.HasIndex("StoreId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("Inventory");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryTaxModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("InventoryId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("InventoryTaxsAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("InventoryTaxsRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("TaxSubTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TaxTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("InventoryId");

                    b.HasIndex("TaxSubTypeId");

                    b.HasIndex("TaxTypeId");

                    b.ToTable("InventoryProductTax");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductCategoryModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductCategoryName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductCategoryName")
                        .IsUnique();

                    b.ToTable("ProductCategory");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<string>("ModifaiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductCategoryId")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductPurchasesDescription")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductSalesDescription")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductTypeId")
                        .HasColumnType("TEXT");

                    b.Property<string>("UnitModelId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductCategoryId");

                    b.HasIndex("ProductId")
                        .IsUnique();

                    b.HasIndex("ProductTypeId");

                    b.HasIndex("UnitModelId");

                    b.ToTable("Product");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTaxModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ProductTaxsRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<string>("TaxSubTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TaxTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TaxSubTypeId");

                    b.HasIndex("TaxTypeId");

                    b.ToTable("ProductTax");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTypeModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductTypeName")
                        .IsUnique();

                    b.ToTable("ProductType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductUnitModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductUnitId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ProductUnitRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ProductUnitSalesPrice")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUnitId");

                    b.ToTable("ProductUnit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.StoreModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("StoreName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("StoreName")
                        .IsUnique();

                    b.ToTable("Store");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.UnitModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("UnitName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UnitName")
                        .IsUnique();

                    b.ToTable("Unit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermDateDrivenModel", b =>
                {
                    b.HasBaseType("SimpleBooks.Models.Model.Treasury.PaymentTermModel");

                    b.Property<int>("DueNextMonthWithinDays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("IfPaidBeforeDayOfMonth")
                        .HasColumnType("INTEGER");

                    b.Property<int>("NetDueBeforeDayOfMonth")
                        .HasColumnType("INTEGER");

                    b.ToTable("PaymentTerm");

                    b.HasDiscriminator().HasValue("PaymentTermDateDrivenModel");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermStandardModel", b =>
                {
                    b.HasBaseType("SimpleBooks.Models.Model.Treasury.PaymentTermModel");

                    b.Property<int>("IfPaidWithinDays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("NetDueDays")
                        .HasColumnType("INTEGER");

                    b.ToTable("PaymentTerm");

                    b.HasDiscriminator().HasValue("PaymentTermStandardModel");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.HR.EmployeeModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", "User")
                        .WithOne("Employee")
                        .HasForeignKey("SimpleBooks.Models.Model.HR.EmployeeModel", "UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Bills")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("Bills")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("Bills")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("Vendor");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillReturnModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("BillReturns")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("BillReturns")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("BillReturns")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("Vendor");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.PurchaseOrderLineModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("PurchaseOrderLines")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", "ProductUnit")
                        .WithMany("PurchaseOrderLines")
                        .HasForeignKey("ProductUnitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.PurchaseOrderModel", "PurchaseOrder")
                        .WithMany("PurchaseOrderLines")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("ProductUnit");

                    b.Navigation("PurchaseOrder");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.PurchaseOrderModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("Vendor");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Vendors")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("Vendors")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("Customers")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("Customers")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Customers")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Invoices")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Customer");

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Customer");

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.SalesOrderLineModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("SalesOrderLines")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", "ProductUnit")
                        .WithMany("SalesOrderLines")
                        .HasForeignKey("ProductUnitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Sales.SalesOrderModel", "SalesOrder")
                        .WithMany("SalesOrderLines")
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("ProductUnit");

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.SalesOrderModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("SalesOrders")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Customer");

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxTypeModel", "TaxType")
                        .WithMany("TaxSubTypes")
                        .HasForeignKey("TaxTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("TaxType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("BankAccounts")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Bank");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", "BankAccount")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.BeneficiaryTypeModel", "BeneficiaryType")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("BeneficiaryTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "Employee")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Bank");

                    b.Navigation("BankAccount");

                    b.Navigation("BeneficiaryType");

                    b.Navigation("Customer");

                    b.Navigation("Employee");

                    b.Navigation("TransactionType");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", "BankAccount")
                        .WithMany("CheckDeposits")
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("CheckDeposits")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Bank");

                    b.Navigation("BankAccount");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", "CheckVault")
                        .WithMany("CheckReturns")
                        .HasForeignKey("CheckVaultId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", "CheckVaultLocation")
                        .WithMany("CheckReturns")
                        .HasForeignKey("CheckVaultLocationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("CheckVault");

                    b.Navigation("CheckVaultLocation");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusHistoryModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", "CheckClear")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckClearId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", "CheckCollection")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckCollectionId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", "CheckDeposit")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckDepositId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", "CheckReject")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckRejectId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", "CheckReturn")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckReturnId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", "CheckStatusFrom")
                        .WithMany("CheckStatusFromHistories")
                        .HasForeignKey("CheckStatusFromId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", "CheckStatusTo")
                        .WithMany("CheckStatusToHistories")
                        .HasForeignKey("CheckStatusToId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", "CheckTreasuryVoucher")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("CheckClear");

                    b.Navigation("CheckCollection");

                    b.Navigation("CheckDeposit");

                    b.Navigation("CheckReject");

                    b.Navigation("CheckReturn");

                    b.Navigation("CheckStatusFrom");

                    b.Navigation("CheckStatusTo");

                    b.Navigation("CheckTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", "BankAccount")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.BeneficiaryTypeModel", "BeneficiaryType")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("BeneficiaryTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", "CheckClear")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckClearId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", "CheckCollection")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckCollectionId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", "CheckDeposit")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckDepositId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", "CheckReject")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckRejectId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", "CheckReturn")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckReturnId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", "CheckStatus")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckStatusId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", "CheckVault")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckVaultId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", "CheckVaultLocation")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckVaultLocationId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "Employee")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Bank");

                    b.Navigation("BankAccount");

                    b.Navigation("BeneficiaryType");

                    b.Navigation("CheckClear");

                    b.Navigation("CheckCollection");

                    b.Navigation("CheckDeposit");

                    b.Navigation("CheckReject");

                    b.Navigation("CheckReturn");

                    b.Navigation("CheckStatus");

                    b.Navigation("CheckVault");

                    b.Navigation("CheckVaultLocation");

                    b.Navigation("Customer");

                    b.Navigation("Employee");

                    b.Navigation("TransactionType");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", "CheckVault")
                        .WithMany("CheckVaultLocations")
                        .HasForeignKey("CheckVaultId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("CheckVault");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.BeneficiaryTypeModel", "BeneficiaryType")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("BeneficiaryTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", "Drawer")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("DrawerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", "DrawerLocation")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("DrawerLocationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "Employee")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("BeneficiaryType");

                    b.Navigation("Customer");

                    b.Navigation("Drawer");

                    b.Navigation("DrawerLocation");

                    b.Navigation("Employee");

                    b.Navigation("TransactionType");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", "Drawer")
                        .WithMany("DrawerLocations")
                        .HasForeignKey("DrawerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Drawer");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.TreasuryLineModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", "BankTransferTreasuryVoucher")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("BankTransferTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillModel", "Bill")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("BillId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillReturnModel", "BillReturn")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("BillReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", "CashTreasuryVoucher")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("CashTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", "CheckTreasuryVoucher")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("CheckTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.ExpensesModel", "Expenses")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("ExpensesId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceModel", "Invoice")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", "InvoiceReturn")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("InvoiceReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("BankTransferTreasuryVoucher");

                    b.Navigation("Bill");

                    b.Navigation("BillReturn");

                    b.Navigation("CashTreasuryVoucher");

                    b.Navigation("CheckTreasuryVoucher");

                    b.Navigation("Expenses");

                    b.Navigation("Invoice");

                    b.Navigation("InvoiceReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.RefreshTokenModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileDetailsModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", "ScreensAccessProfile")
                        .WithMany("ScreensAccessProfileDetails")
                        .HasForeignKey("ScreensAccessProfileId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("ScreensAccessProfile");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", null)
                        .WithMany("ScreensAccessProfiles")
                        .HasForeignKey("UserModelId");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.SettingModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", "User")
                        .WithOne("Setting")
                        .HasForeignKey("SimpleBooks.Models.Model.User.SettingModel", "UserId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.UserModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", "ScreensAccessProfile")
                        .WithMany("Users")
                        .HasForeignKey("ScreensAccessProfileId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("ScreensAccessProfile");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillModel", "Bill")
                        .WithMany("Inventories")
                        .HasForeignKey("BillId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillReturnModel", "BillReturn")
                        .WithMany("Inventories")
                        .HasForeignKey("BillReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceModel", "Invoice")
                        .WithMany("Inventories")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", "InvoiceReturn")
                        .WithMany("Inventories")
                        .HasForeignKey("InvoiceReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("Inventories")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", "ProductUnit")
                        .WithMany("Inventories")
                        .HasForeignKey("ProductUnitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.PurchaseOrderModel", "PurchaseOrder")
                        .WithMany("Inventories")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.PurchaseOrderLineModel", "PurchaseOrderLine")
                        .WithMany("Inventories")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.SalesOrderModel", "SalesOrder")
                        .WithMany("Inventories")
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.SalesOrderLineModel", "SalesOrderLine")
                        .WithMany("Inventories")
                        .HasForeignKey("SalesOrderLineId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.StoreModel", "Store")
                        .WithMany("Inventories")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("Inventories")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Bill");

                    b.Navigation("BillReturn");

                    b.Navigation("Invoice");

                    b.Navigation("InvoiceReturn");

                    b.Navigation("Product");

                    b.Navigation("ProductUnit");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("PurchaseOrderLine");

                    b.Navigation("SalesOrder");

                    b.Navigation("SalesOrderLine");

                    b.Navigation("Store");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryTaxModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.InventoryModel", "Inventory")
                        .WithMany("InventoryTaxes")
                        .HasForeignKey("InventoryId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", "TaxSubType")
                        .WithMany("InventoryTaxes")
                        .HasForeignKey("TaxSubTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxTypeModel", "TaxType")
                        .WithMany("InventoryTaxes")
                        .HasForeignKey("TaxTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Inventory");

                    b.Navigation("TaxSubType");

                    b.Navigation("TaxType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductCategoryModel", "ProductCategory")
                        .WithMany("Products")
                        .HasForeignKey("ProductCategoryId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductTypeModel", "ProductType")
                        .WithMany("Products")
                        .HasForeignKey("ProductTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", null)
                        .WithMany("Products")
                        .HasForeignKey("UnitModelId");

                    b.Navigation("ProductCategory");

                    b.Navigation("ProductType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTaxModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("ProductTaxes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", "TaxSubType")
                        .WithMany("ProductTaxes")
                        .HasForeignKey("TaxSubTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxTypeModel", "TaxType")
                        .WithMany("ProductTaxes")
                        .HasForeignKey("TaxTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("TaxSubType");

                    b.Navigation("TaxType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductUnitModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("ProductUnits")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", "ProductUnit")
                        .WithMany("ProductUnits")
                        .HasForeignKey("ProductUnitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("ProductUnit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.BeneficiaryTypeModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.HR.EmployeeModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("Customers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillReturnModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.PurchaseOrderLineModel", b =>
                {
                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.PurchaseOrderModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("PurchaseOrderLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("BillReturns");

                    b.Navigation("Bills");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorTypeModel", b =>
                {
                    b.Navigation("BillReturns");

                    b.Navigation("Bills");

                    b.Navigation("PurchaseOrders");

                    b.Navigation("Vendors");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerTypeModel", b =>
                {
                    b.Navigation("Customers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.SalesOrderLineModel", b =>
                {
                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.SalesOrderModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("SalesOrderLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", b =>
                {
                    b.Navigation("InventoryTaxes");

                    b.Navigation("ProductTaxes");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxTypeModel", b =>
                {
                    b.Navigation("InventoryTaxes");

                    b.Navigation("ProductTaxes");

                    b.Navigation("TaxSubTypes");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.TransactionTypeModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CheckDeposits");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", b =>
                {
                    b.Navigation("BankAccounts");

                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CheckDeposits");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", b =>
                {
                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", b =>
                {
                    b.Navigation("CheckStatusFromHistories");

                    b.Navigation("CheckStatusToHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", b =>
                {
                    b.Navigation("CheckReturns");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", b =>
                {
                    b.Navigation("CheckReturns");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("CheckVaultLocations");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", b =>
                {
                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", b =>
                {
                    b.Navigation("CashTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", b =>
                {
                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("DrawerLocations");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.ExpensesModel", b =>
                {
                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermModel", b =>
                {
                    b.Navigation("BillReturns");

                    b.Navigation("Bills");

                    b.Navigation("Customers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");

                    b.Navigation("PurchaseOrders");

                    b.Navigation("SalesOrders");

                    b.Navigation("Vendors");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", b =>
                {
                    b.Navigation("ScreensAccessProfileDetails");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.UserModel", b =>
                {
                    b.Navigation("Employee")
                        .IsRequired();

                    b.Navigation("RefreshTokens");

                    b.Navigation("ScreensAccessProfiles");

                    b.Navigation("Setting")
                        .IsRequired();
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryModel", b =>
                {
                    b.Navigation("InventoryTaxes");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductCategoryModel", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("ProductTaxes");

                    b.Navigation("ProductUnits");

                    b.Navigation("PurchaseOrderLines");

                    b.Navigation("SalesOrderLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTypeModel", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.StoreModel", b =>
                {
                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.UnitModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("ProductUnits");

                    b.Navigation("Products");

                    b.Navigation("PurchaseOrderLines");

                    b.Navigation("SalesOrderLines");
                });
#pragma warning restore 612, 618
        }
    }
}
