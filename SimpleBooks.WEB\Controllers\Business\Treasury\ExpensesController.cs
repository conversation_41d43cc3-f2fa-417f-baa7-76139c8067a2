﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury
{
    public class ExpensesController : BaseBusinessController<
        ExpensesModel,
        ExpensesModel,
        CreateExpensesViewModel,
        UpdateExpensesViewModel,
        IndexExpensesFormViewModel,
        CreateExpensesFormViewModel,
        UpdateExpensesFormViewModel>
    {
        private readonly IExpensesService _expensesService;

        public ExpensesController(IExpensesService expensesService) : base(expensesService)
        {
            _expensesService = expensesService;
        }

        public override async Task<IActionResult> Create(CreateExpensesFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _expensesService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("expenses", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            ExpensesModel? entity = await _expensesService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateExpensesFormViewModel viewModel = new UpdateExpensesFormViewModel()
            {
                Id = entity.Id,
                ExpensesName = entity.ExpensesName,
                ExpensesDescription = entity.ExpensesDescription,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateExpensesFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _expensesService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("expenses", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateExpensesViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateExpensesViewModel> createEntites = new List<CreateExpensesViewModel>();
            var createEntitesDict = new Dictionary<string, CreateExpensesViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateExpensesViewModel
                    {
                        ExpensesName = row.Cell(1).GetString(),
                        ExpensesDescription = row.Cell(2).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateExpensesFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateExpensesFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
