﻿namespace SimpleBooks.WEB.Controllers.Business.Sales
{
    public class CustomerTypeController : BaseBusinessController<
        CustomerTypeModel,
        CustomerTypeModel,
        CreateCustomerTypeViewModel,
        UpdateCustomerTypeViewModel,
        IndexCustomerTypeFormViewModel,
        CreateCustomerTypeFormViewModel,
        UpdateCustomerTypeFormViewModel>
    {
        private readonly ICustomerTypeService _customerTypeService;

        public CustomerTypeController(ICustomerTypeService customerTypeService) : base(customerTypeService)
        {
            _customerTypeService = customerTypeService;
        }

        public override async Task<IActionResult> Create(CreateCustomerTypeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _customerTypeService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("customerType", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CustomerTypeModel? entity = await _customerTypeService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateCustomerTypeFormViewModel viewModel = new UpdateCustomerTypeFormViewModel()
            {
                Id = entity.Id,
                CustomerTypeName = entity.CustomerTypeName,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateCustomerTypeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _customerTypeService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("customerType", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateCustomerTypeViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateCustomerTypeViewModel> createEntites = new List<CreateCustomerTypeViewModel>();
            var createEntitesDict = new Dictionary<string, CreateCustomerTypeViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateCustomerTypeViewModel
                    {
                        CustomerTypeName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCustomerTypeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCustomerTypeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
