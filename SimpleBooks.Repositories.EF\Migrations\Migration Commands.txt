﻿Add-Migration 'SimpleBooks_002' -OutputDir Migrations\MySQLMigrations -Args 'MySQL' -Context MySQLApplicationDBContext -Project SimpleBooks.Repositories.EF
Update-Database -Args 'MySQL' -Context MySQLApplicationDBContext

Add-Migration 'SimpleBooks_002' -OutputDir Migrations\SQLiteMigrations -Args 'SQLite' -Context SQLiteApplicationDBContext -Project SimpleBooks.Repositories.EF
Update-Database -Args 'SQLite' -Context SQLiteApplicationDBContext

Update-Database 'SimpleBooks_001' -Args 'MySQL' -Context MySQLApplicationDBContext
Update-Database 'SimpleBooks_001' -Args 'SQLite' -Context SQLiteApplicationDBContext
remove-Migration -Args 'MySQL' -Context MySQLApplicationDBContext
remove-Migration -Args 'SQLite' -Context SQLiteApplicationDBContext
