﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement
{
    public class BankService : SimpleBooksBaseService<BankModel, BankModel, CreateBankViewModel, UpdateBankViewModel>, IBankService
    {
        private readonly IUnitOfWork _unitOfWork;

        public BankService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.Bank)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<BankModel>, IIncludableQueryable<BankModel, object>>? Includes =>
            x => x
            .Include(xx => xx.BankAccounts);

        public override void EditModelBeforeSave(BankModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var item in model.BankAccounts)
                item.BankId = model.Id;
        }

        public override void ValidateEntity(BankModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<BankModel> repositorySpecifications = new RepositorySpecifications<BankModel>()
                {
                    SearchValue = x => x.BankName == model.BankName,
                    IsTackable = false,
                };
                var isExistingBnak = _unitOfWork.Bank.Get(repositorySpecifications);
                if (isExistingBnak != null && isExistingBnak.Id != model.Id)
                    throw new ValidationException("Bank with the same Name already exists.");
            }
            if (model.BankAccounts.Count == 0)
                throw new ValidationException("At least one bank account is required.");
            if (model.BankAccounts.Count >= 1)
            {
                var hasDuplicates = model.BankAccounts.GroupBy(x => x.BankAccountNumber).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Bank accounts must be unique.");
                var hasInvalidBankAccountNumber = model.BankAccounts.Any(x => string.IsNullOrWhiteSpace(x.BankAccountNumber));
                if (hasInvalidBankAccountNumber)
                    throw new ValidationException("Bank account number must not be empty.");
            }
        }
    }
}
