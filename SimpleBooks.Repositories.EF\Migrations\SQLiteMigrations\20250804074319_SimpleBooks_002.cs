﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_002 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_VendorType_VendorTypeName",
                table: "VendorType",
                column: "VendorTypeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Vendor_VendorName",
                table: "Vendor",
                column: "VendorName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Vendor_VendorTaxCardNumber",
                table: "Vendor",
                column: "VendorTaxCardNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_User_UserName",
                table: "User",
                column: "UserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Unit_UnitName",
                table: "Unit",
                column: "UnitName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Store_StoreName",
                table: "Store",
                column: "StoreName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_SalesOrderId",
                table: "SalesOrder",
                column: "SalesOrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_PurchaseOrderId",
                table: "PurchaseOrder",
                column: "PurchaseOrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductType_ProductTypeName",
                table: "ProductType",
                column: "ProductTypeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductCategory_ProductCategoryName",
                table: "ProductCategory",
                column: "ProductCategoryName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Product_ProductId",
                table: "Product",
                column: "ProductId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PaymentTerm_PaymentTermName",
                table: "PaymentTerm",
                column: "PaymentTermName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReturn_InvoiceReturnId",
                table: "InvoiceReturn",
                column: "InvoiceReturnId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Invoice_InvoiceId",
                table: "Invoice",
                column: "InvoiceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Expenses_ExpensesName",
                table: "Expenses",
                column: "ExpensesName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Employee_EmployeeName",
                table: "Employee",
                column: "EmployeeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Drawer_DrawerName",
                table: "Drawer",
                column: "DrawerName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerType_CustomerTypeName",
                table: "CustomerType",
                column: "CustomerTypeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Customer_CustomerName",
                table: "Customer",
                column: "CustomerName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Customer_CustomerTaxCardNumber",
                table: "Customer",
                column: "CustomerTaxCardNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CheckVault_CheckVaultName",
                table: "CheckVault",
                column: "CheckVaultName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BillReturn_BillReturnId",
                table: "BillReturn",
                column: "BillReturnId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Bill_BillId",
                table: "Bill",
                column: "BillId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Bank_BankName",
                table: "Bank",
                column: "BankName",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_VendorType_VendorTypeName",
                table: "VendorType");

            migrationBuilder.DropIndex(
                name: "IX_Vendor_VendorName",
                table: "Vendor");

            migrationBuilder.DropIndex(
                name: "IX_Vendor_VendorTaxCardNumber",
                table: "Vendor");

            migrationBuilder.DropIndex(
                name: "IX_User_UserName",
                table: "User");

            migrationBuilder.DropIndex(
                name: "IX_Unit_UnitName",
                table: "Unit");

            migrationBuilder.DropIndex(
                name: "IX_Store_StoreName",
                table: "Store");

            migrationBuilder.DropIndex(
                name: "IX_SalesOrder_SalesOrderId",
                table: "SalesOrder");

            migrationBuilder.DropIndex(
                name: "IX_PurchaseOrder_PurchaseOrderId",
                table: "PurchaseOrder");

            migrationBuilder.DropIndex(
                name: "IX_ProductType_ProductTypeName",
                table: "ProductType");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategory_ProductCategoryName",
                table: "ProductCategory");

            migrationBuilder.DropIndex(
                name: "IX_Product_ProductId",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_PaymentTerm_PaymentTermName",
                table: "PaymentTerm");

            migrationBuilder.DropIndex(
                name: "IX_InvoiceReturn_InvoiceReturnId",
                table: "InvoiceReturn");

            migrationBuilder.DropIndex(
                name: "IX_Invoice_InvoiceId",
                table: "Invoice");

            migrationBuilder.DropIndex(
                name: "IX_Expenses_ExpensesName",
                table: "Expenses");

            migrationBuilder.DropIndex(
                name: "IX_Employee_EmployeeName",
                table: "Employee");

            migrationBuilder.DropIndex(
                name: "IX_Drawer_DrawerName",
                table: "Drawer");

            migrationBuilder.DropIndex(
                name: "IX_CustomerType_CustomerTypeName",
                table: "CustomerType");

            migrationBuilder.DropIndex(
                name: "IX_Customer_CustomerName",
                table: "Customer");

            migrationBuilder.DropIndex(
                name: "IX_Customer_CustomerTaxCardNumber",
                table: "Customer");

            migrationBuilder.DropIndex(
                name: "IX_CheckVault_CheckVaultName",
                table: "CheckVault");

            migrationBuilder.DropIndex(
                name: "IX_BillReturn_BillReturnId",
                table: "BillReturn");

            migrationBuilder.DropIndex(
                name: "IX_Bill_BillId",
                table: "Bill");

            migrationBuilder.DropIndex(
                name: "IX_Bank_BankName",
                table: "Bank");
        }
    }
}
