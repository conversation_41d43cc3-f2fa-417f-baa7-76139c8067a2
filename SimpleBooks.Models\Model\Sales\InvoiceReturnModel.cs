﻿namespace SimpleBooks.Models.Model.Sales
{
    [Table("InvoiceReturn")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<InvoiceReturnModel>))]
    public class InvoiceReturnModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Invoice Return Id")]
        public string InvoiceReturnId { get; set; }
        [CustomRequired]
        [DisplayName("Invoice Return Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly InvoiceReturnDate { get; set; }
        [CustomRequired]
        [DisplayName("Invoice Return Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly InvoiceReturnDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Customer")]
        public Ulid CustomerId { get; set; }
        public virtual CustomerModel? Customer { get; set; }

        [DisplayName("Customer Type")]
        public Ulid? CustomerTypeId { get; set; }
        public virtual CustomerTypeModel? CustomerType { get; set; }

        [DisplayName("Customer Sales Rep")]
        public Ulid? CustomerRepId { get; set; }
        public virtual EmployeeModel? CustomerRep { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        [CustomRequired]
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        public virtual ICollection<TreasuryLineModel> TreasuryLines { get; set; } = new List<TreasuryLineModel>();
    }
}
