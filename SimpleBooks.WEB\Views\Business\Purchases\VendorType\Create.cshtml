﻿@model CreateVendorTypeFormViewModel

@{
    ViewData["Title"] = "Add Vendor Type";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Vendor Type
</h5>

<form asp-controller="VendorType" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="VendorTypeName" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="VendorTypeName" placeholder="Vendor Type Name">
                <span asp-validation-for="VendorTypeName" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}