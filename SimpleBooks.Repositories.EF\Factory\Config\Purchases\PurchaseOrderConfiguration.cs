﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Purchases
{
    public class PurchaseOrderConfiguration : IEntityTypeConfiguration<PurchaseOrderModel>
    {
        public void Configure(EntityTypeBuilder<PurchaseOrderModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.PurchaseOrderId).IsUnique();

            builder.HasOne(d => d.Vendor).WithMany(p => p.PurchaseOrders)
                .HasForeignKey(d => d.VendorId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.VendorType).WithMany(p => p.PurchaseOrders)
                .HasForeignKey(d => d.VendorTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.PurchaseOrders)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
