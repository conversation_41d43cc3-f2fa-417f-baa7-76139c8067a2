﻿namespace SimpleBooks.WEB.Controllers.Business.Warehouse
{
    public class UnitController : BaseBusinessController<
        UnitModel,
        UnitModel,
        CreateUnitViewModel,
        UpdateUnitViewModel,
        IndexUnitFormViewModel,
        CreateUnitFormViewModel,
        UpdateUnitFormViewModel>
    {
        private readonly IUnitService _unitService;

        public UnitController(IUnitService unitService) : base(unitService)
        {
            _unitService = unitService;
        }

        public override async Task<IActionResult> Create(CreateUnitFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _unitService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("unit", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            UnitModel? entity = await _unitService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateUnitFormViewModel viewModel = new UpdateUnitFormViewModel()
            {
                Id = entity.Id,
                UnitName = entity.UnitName,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateUnitFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _unitService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("unit", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateUnitViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateUnitViewModel> createEntites = new List<CreateUnitViewModel>();
            var createEntitesDict = new Dictionary<string, CreateUnitViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateUnitViewModel
                    {
                        UnitName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateUnitFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateUnitFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
