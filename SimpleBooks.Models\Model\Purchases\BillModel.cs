﻿namespace SimpleBooks.Models.Model.Purchases
{
    [Table("Bill")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BillModel>))]
    public class BillModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Bill Id")]
        public string BillId { get; set; }
        [CustomRequired]
        [DisplayName("Bill Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly BillDate { get; set; }
        [CustomRequired]
        [DisplayName("Bill Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly BillDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Vendor")]
        public Ulid VendorId { get; set; }
        public virtual VendorModel? Vendor { get; set; }

        [DisplayName("Vendor Type")]
        public Ulid? VendorTypeId { get; set; }
        public virtual VendorTypeModel? VendorType { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        [DisplayName("Document Discount Type")]
        public Ulid? DocumentDiscountTypeId { get; set; }
        public virtual DiscountTypeModel? DocumentDiscountType { get; set; }

        [DisplayName("Document Discount Rate")]
        [Range(0, 100)]
        public decimal DocumentDiscountRate { get; set; }

        [DisplayName("Document Discount Amount")]
        public decimal DocumentDiscountAmount { get; set; }

        [CustomRequired]
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        public virtual ICollection<TreasuryLineModel> TreasuryLines { get; set; } = new List<TreasuryLineModel>();
    }
}
