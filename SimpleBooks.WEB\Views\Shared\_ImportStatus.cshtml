﻿@{
    ImportResult importResult = Model;
    if (importResult != null)
    {
        var alertClass = importResult.ErrorCount == 0 ? "alert-info" : "alert-danger";
        <div class="alert @alertClass mt-5">
            <h4 class="alert-heading">Importing Data Status!</h4>
            <p class="mb-0">Success Count => @importResult.SuccessCount</p>
            <p class="mb-0">Error Count => @importResult.ErrorCount</p>
            <p class="mb-0">Total Count => @importResult.TotalCount</p>
            @if (importResult.Errors.Count > 0)
            {
                <h5 class="alert-heading pt-2">Importing Data Errors:</h5>
                <div class="mb-0 ps-2">
                    @foreach (var error in importResult.Errors)
                    {
                        <p class="mb-0">@error</p>
                    }
                </div>
            }
        </div>
	}
}
