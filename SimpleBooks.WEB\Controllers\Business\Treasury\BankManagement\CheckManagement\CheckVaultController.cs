﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultController : BaseBusinessController<
        CheckVaultModel,
        CheckVaultModel,
        CreateCheckVaultViewModel,
        UpdateCheckVaultViewModel,
        IndexCheckVaultFormViewModel,
        CreateCheckVaultFormViewModel,
        UpdateCheckVaultFormViewModel>
    {
        private readonly ICheckVaultService _checkVaultService;

        public CheckVaultController(ICheckVaultService checkVaultService) : base(checkVaultService)
        {
            _checkVaultService = checkVaultService;
        }

        public override async Task<IActionResult> Create(CreateCheckVaultFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _checkVaultService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkVault", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CheckVaultModel? entity = await _checkVaultService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateCheckVaultFormViewModel viewModel = new UpdateCheckVaultFormViewModel()
            {
                Id = entity.Id,
                CheckVaultName = entity.CheckVaultName,
                CheckVaultLocations = entity.CheckVaultLocations.Select(x => x.ToUpdateDto()).ToList(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateCheckVaultFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _checkVaultService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkVault", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateCheckVaultViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateCheckVaultViewModel> createEntites = new List<CreateCheckVaultViewModel>();
            var createEntitesDict = new Dictionary<string, CreateCheckVaultViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateCheckVaultViewModel
                    {
                        CheckVaultName = mainKey,
                        CheckVaultLocations = new List<CreateCheckVaultLocationViewModel>(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }

                var locationNumber = row.Cell(2).GetString();
                if (!string.IsNullOrEmpty(locationNumber))
                {
                    existingKey.CheckVaultLocations.Add(new CreateCheckVaultLocationViewModel
                    {
                        CheckVaultLocationNumber = locationNumber,
                        CheckVaultLocationCurrency = row.Cell(3).GetString(),
                    });
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCheckVaultFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCheckVaultFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
