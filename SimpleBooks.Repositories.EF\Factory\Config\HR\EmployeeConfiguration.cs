﻿namespace SimpleBooks.Repositories.EF.Factory.Config.HR
{
    public class EmployeeConfiguration : IEntityTypeConfiguration<EmployeeModel>
    {
        public void Configure(EntityTypeBuilder<EmployeeModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasIndex(x => x.EmployeeName).IsUnique();

            builder.HasData(Data.GetEmployees());
        }
    }
}
