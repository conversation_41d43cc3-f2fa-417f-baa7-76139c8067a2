﻿namespace SimpleBooks.Models.Model.Sales
{
    [Table("Invoice")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<InvoiceModel>))]
    public class InvoiceModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Invoice Id")]
        public string InvoiceId { get; set; }
        [CustomRequired]
        [DisplayName("Invoice Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly InvoiceDate { get; set; }
        [CustomRequired]
        [DisplayName("Invoice Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly InvoiceDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Customer")]
        public Ulid CustomerId { get; set; }
        public virtual CustomerModel? Customer { get; set; }

        [DisplayName("Customer Type")]
        public Ulid? CustomerTypeId { get; set; }
        public virtual CustomerTypeModel? CustomerType { get; set; }

        [DisplayName("Customer Sales Rep")]
        public Ulid? CustomerRepId { get; set; }
        public virtual EmployeeModel? CustomerRep { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        [DisplayName("Document Discount Type")]
        public Ulid? DocumentDiscountTypeId { get; set; }
        public virtual DiscountTypeModel? DocumentDiscountType { get; set; }

        [DisplayName("Document Discount Rate")]
        [Range(0, 100)]
        public decimal DocumentDiscountRate { get; set; }

        [DisplayName("Document Discount Amount")]
        public decimal DocumentDiscountAmount { get; set; }

        [CustomRequired]
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        public virtual ICollection<TreasuryLineModel> TreasuryLines { get; set; } = new List<TreasuryLineModel>();
    }
}
