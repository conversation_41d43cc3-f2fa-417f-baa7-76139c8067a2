﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.CashManagement
{
    public class DrawerController : BaseBusinessController<
        DrawerModel,
        DrawerModel,
        CreateDrawerViewModel,
        UpdateDrawerViewModel,
        IndexDrawerFormViewModel,
        CreateDrawerFormViewModel,
        UpdateDrawerFormViewModel>
    {
        private readonly IDrawerService _drawerService;

        public DrawerController(IDrawerService drawerService) : base(drawerService)
        {
            _drawerService = drawerService;
        }

        public override async Task<IActionResult> Create(CreateDrawerFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _drawerService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("drawer", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            DrawerModel? entity = await _drawerService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateDrawerFormViewModel viewModel = new UpdateDrawerFormViewModel()
            {
                Id = entity.Id,
                DrawerName = entity.DrawerName,
                DrawerLocations = entity.DrawerLocations.Select(x => x.ToUpdateDto()).ToList(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateDrawerFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _drawerService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("drawer", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateDrawerViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateDrawerViewModel> createEntites = new List<CreateDrawerViewModel>();
            var createEntitesDict = new Dictionary<string, CreateDrawerViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateDrawerViewModel
                    {
                        DrawerName = mainKey,
                        DrawerLocations = new List<CreateDrawerLocationViewModel>(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }

                var locationNumber = row.Cell(2).GetString();
                if (!string.IsNullOrEmpty(locationNumber))
                {
                    existingKey.DrawerLocations.Add(new CreateDrawerLocationViewModel
                    {
                        DrawerLocationNumber = locationNumber,
                        DrawerLocationCurrency = row.Cell(3).GetString(),
                    });
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateDrawerFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateDrawerFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
