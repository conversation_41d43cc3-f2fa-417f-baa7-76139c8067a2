﻿@model CreateInvoiceReturnFormViewModel

@{
    ViewData["Title"] = "Add Invoice Return";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Invoice Return
</h5>

<form asp-controller="InvoiceReturn" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="InvoiceReturnId" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="InvoiceReturnId" placeholder="InvoiceReturn Id">
                <span asp-validation-for="InvoiceReturnId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="InvoiceReturnDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="InvoiceReturnDate" placeholder="InvoiceReturn Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="InvoiceReturnDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CustomerId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CustomerId"
                        data-placeholder="Select a customer" data-minimum-results-for-search="Infinity" onchange="handler.onCustomerChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.Customers)
                    {
                        <option value="@item.Id">@item.CustomerName</option>
                    }
                </select>
                <span asp-validation-for="CustomerId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CustomerTypeId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CustomerTypeId" asp-items="Model.CustomerTypes"
                        data-placeholder="Select a customer type" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                </select>
                <span asp-validation-for="CustomerTypeId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CustomerRepId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CustomerRepId" asp-items="Model.CustomerReps"
                        data-placeholder="Select a customer rep" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                </select>
                <span asp-validation-for="CustomerRepId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PaymentTermId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="PaymentTermId"
                        data-placeholder="Select a payment term" data-minimum-results-for-search="Infinity" onchange="handler.onPaymentTermChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.PaymentTerms)
                    {
                        <option value="@item.Id">@item.PaymentTermName</option>
                    }
                </select>
                <span asp-validation-for="PaymentTermId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="InvoiceReturnDueDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="InvoiceReturnDueDate" placeholder="InvoiceReturn Due Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="InvoiceReturnDueDate" class="text-danger"></span>
            </div>
        </div>
        <div class="row">
            @if (Model.Inventories != null)
            {
                var inventoryViewData = new ViewDataDictionary(ViewData);
                string transactionTypes = JsonUtilities.SaveToJsonString(TransactionTypeEnumeration.TransactionTypeEnumerations);
                inventoryViewData.Add("TransactionTypes", transactionTypes);
                inventoryViewData.Add("TransactionType", "Invoice Return");
                @await Html.PartialAsync("_CreateInventoryPartial", Model, inventoryViewData)
                ;
            }
        </div>
        <div class="col-md-6 mt-2">
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
	<script src="~/js/tools.js"></script>
    <script type="module">
        import { InvoiceReturnHandler } from '/js/business/Sales/invoiceReturnHandler.js';
        const customers = @Html.Raw(Json.Serialize(Model.Customers));
        const paymentTerms = @Html.Raw(Json.Serialize(Model.PaymentTerms));

        window.handler = new InvoiceReturnHandler(
            customers,
            paymentTerms
        );

        import { InventoryHandler } from '/js/business/Warehouse/inventoryHandler.js';
        const products = @Html.Raw(Json.Serialize(Model.Products));
        const stores = @Html.Raw(Json.Serialize(Model.SelectiveStores));
        const taxTypes = @Html.Raw(Json.Serialize(Model.TaxTypes));
        const taxSubTypes = @Html.Raw(Json.Serialize(Model.TaxSubTypes));

        window.inventoryHandler = new InventoryHandler(
            products,
            stores,
            taxTypes,
            taxSubTypes,
            false,
            null,
            true
        );
    </script>
}
