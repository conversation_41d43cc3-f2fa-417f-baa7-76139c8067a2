namespace SimpleBooks.Models.Enumerations
{
    public class DiscountTypeEnumeration : UlidEnumeration<DiscountTypeEnumeration>
    {
        public static readonly DiscountTypeEnumeration Percentage = new DiscountTypeEnumeration(Ulid.Parse("01JRKA5XSNHEQWKTR0NCJWYC6W"), "Percentage");
        public static readonly DiscountTypeEnumeration Amount = new DiscountTypeEnumeration(Ulid.Parse("01JRKA5XSQ45QDM1C7H1TME01A"), "Amount");

        private DiscountTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<DiscountTypeEnumeration> DiscountTypeEnumerations
        {
            get => new List<DiscountTypeEnumeration>
            {
                Percentage, Amount
            };
        }
    }
}
