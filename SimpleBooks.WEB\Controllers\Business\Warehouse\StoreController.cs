﻿namespace SimpleBooks.WEB.Controllers.Business.Warehouse
{
    public class StoreController : BaseBusinessController<
        StoreModel,
        StoreModel,
        CreateStoreViewModel,
        UpdateStoreViewModel,
        IndexStoreFormViewModel,
        CreateStoreFormViewModel,
        UpdateStoreFormViewModel>
    {
        private readonly IStoreService _storeService;

        public StoreController(IStoreService storeService) : base(storeService)
        {
            _storeService = storeService;
        }

        public override async Task<IActionResult> Create(CreateStoreFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _storeService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("store", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            StoreModel? entity = await _storeService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateStoreFormViewModel viewModel = new UpdateStoreFormViewModel()
            {
                Id = entity.Id,
                StoreName = entity.StoreName,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateStoreFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _storeService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("store", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateStoreViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateStoreViewModel> createEntites = new List<CreateStoreViewModel>();
            var createEntitesDict = new Dictionary<string, CreateStoreViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateStoreViewModel
                    {
                        StoreName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateStoreFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateStoreFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
