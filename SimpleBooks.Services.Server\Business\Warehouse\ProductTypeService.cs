﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class ProductTypeService : SimpleBooksBaseService<ProductTypeModel, ProductTypeModel, CreateProductTypeViewModel, UpdateProductTypeViewModel>, IProductTypeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductTypeService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.ProductType)
        {
            _unitOfWork = unitOfWork;
        }

        public override void ValidateEntity(ProductTypeModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<ProductTypeModel> repositorySpecifications = new RepositorySpecifications<ProductTypeModel>()
                {
                    SearchValue = x => x.ProductTypeName == model.ProductTypeName,
                    IsTackable = false,
                };
                var isExistingProductType = _unitOfWork.ProductType.Get(repositorySpecifications);
                if (isExistingProductType != null && isExistingProductType.Id != model.Id)
                    throw new ValidationException("Product Type with the same Name already exists.");
            }
        }
    }
}
