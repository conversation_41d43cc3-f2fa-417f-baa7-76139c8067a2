﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Purchases
{
    public class VendorConfiguration : IEntityTypeConfiguration<VendorModel>
    {
        public void Configure(EntityTypeBuilder<VendorModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.VendorName).IsUnique();
            builder.HasIndex(x => x.VendorTaxCardNumber).IsUnique();

            builder.HasOne(d => d.VendorType).WithMany(p => p.Vendors)
                .HasForeignKey(d => d.VendorTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.Vendors)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
