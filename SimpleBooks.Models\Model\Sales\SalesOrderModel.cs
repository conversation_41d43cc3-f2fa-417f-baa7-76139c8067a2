﻿namespace SimpleBooks.Models.Model.Sales
{
    [Table("SalesOrder")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<SalesOrderModel>))]
    public class SalesOrderModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Sales Order Id")]
        public string SalesOrderId { get; set; }
        [CustomRequired]
        [DisplayName("Sales Order Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly SalesOrderDate { get; set; }
        [CustomRequired]
        [DisplayName("Sales Order Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly SalesOrderDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Customer")]
        public Ulid CustomerId { get; set; }
        public virtual CustomerModel? Customer { get; set; }

        [DisplayName("Customer Type")]
        public Ulid? CustomerTypeId { get; set; }
        public virtual CustomerTypeModel? CustomerType { get; set; }

        [DisplayName("Customer Sales Rep")]
        public Ulid? CustomerRepId { get; set; }
        public virtual EmployeeModel? CustomerRep { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        [DisplayName("Document Discount Type")]
        public Ulid? DocumentDiscountTypeId { get; set; }
        public virtual DiscountTypeModel? DocumentDiscountType { get; set; }

        [DisplayName("Document Discount Rate")]
        [Range(0, 100)]
        public decimal DocumentDiscountRate { get; set; }

        [DisplayName("Document Discount Amount")]
        public decimal DocumentDiscountAmount { get; set; }

        [CustomRequired]
        [DisplayName("Sales Order Lines")]
        public virtual ICollection<SalesOrderLineModel> SalesOrderLines { get; set; } = new List<SalesOrderLineModel>();
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
    }
}
