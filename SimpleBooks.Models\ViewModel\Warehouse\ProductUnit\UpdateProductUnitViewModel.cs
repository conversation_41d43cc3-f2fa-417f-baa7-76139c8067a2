﻿namespace SimpleBooks.Models.ViewModel.Warehouse.ProductUnit
{
    public class UpdateProductUnitViewModel : BaseUpdateViewModel, IEntityMapper<ProductUnitModel, UpdateProductUnitViewModel>
    {
        [DisplayName("Product Unit Sales Price")]
        public decimal ProductUnitSalesPrice { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit Ratio")]
        public decimal ProductUnitRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }

        public UpdateProductUnitViewModel ToDto(ProductUnitModel entity) => entity.ToUpdateDto();

        public ProductUnitModel ToEntity() => ProductUnitMapper.ToEntity(this);
    }
}
