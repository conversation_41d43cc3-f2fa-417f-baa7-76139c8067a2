﻿namespace SimpleBooks.Repositories.EF.Factory.Config.User
{
    public class UserConfiguration : IEntityTypeConfiguration<UserModel>
    {
        public void Configure(EntityTypeBuilder<UserModel> builder)
        {
            // Remove explicit key configuration as it should be defined on the base class
            // builder.HasKey(x => new { x.Id });

            builder.HasIndex(x => x.UserName).IsUnique();

            builder.HasOne(d => d.Employee).WithOne(p => p.User)
                .HasForeignKey<EmployeeModel>(d => d.UserId)
                .IsRequired(false);

            builder.HasOne(d => d.Setting).WithOne(p => p.User)
                .HasForeignKey<SettingModel>(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.ScreensAccessProfile).WithMany(p => p.Users)
                .HasForeignKey(d => d.ScreensAccessProfileId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasData(Data.GetUsers());
        }
    }
}
