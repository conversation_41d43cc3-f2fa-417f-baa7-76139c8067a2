﻿namespace SimpleBooks.WEB.Controllers.Business.Sales
{
    public class CustomerController : BaseBusinessController<
        CustomerModel,
        IndexCustomerViewModel,
        CreateCustomerViewModel,
        UpdateCustomerViewModel,
        IndexCustomerFormViewModel,
        CreateCustomerFormViewModel,
        UpdateCustomerFormViewModel>
    {
        private readonly ICustomerService _customerService;

        public CustomerController(ICustomerService customerService) : base(customerService)
        {
            _customerService = customerService;
        }

        public override async Task<IActionResult> Create(CreateCustomerFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _customerService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("customer", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CustomerModel? entity = await _customerService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateCustomerFormViewModel viewModel = new UpdateCustomerFormViewModel()
            {
                Id = entity.Id,
                CustomerName = entity.CustomerName,
                CustomerTaxCardNumber = entity.CustomerTaxCardNumber,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                CustomerTypes = await _customerService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync(),
                CustomerReps = await _customerService.SelectiveCustomerRepListAsync().ToSelectListItemAsync(),
                PaymentTerms = await _customerService.SelectivePaymentTermListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateCustomerFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _customerService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("customer", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateCustomerViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateCustomerViewModel> createEntites = new List<CreateCustomerViewModel>();
            var createEntitesDict = new Dictionary<string, CreateCustomerViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateCustomerViewModel
                    {
                        CustomerName = row.Cell(1).GetString(),
                        CustomerTaxCardNumber = row.Cell(2).GetString(),
                        CustomerTypeId = ValidateValue.ValidateUlid(row.Cell(3).GetString()),
                        CustomerRepId = ValidateValue.ValidateUlid(row.Cell(4).GetString()),
                        PaymentTermId = ValidateValue.ValidateUlid(row.Cell(5).GetString()),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCustomerFormViewModel model)
        {
            model.CustomerTypes = await _customerService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync();
            model.CustomerReps = await _customerService.SelectiveCustomerRepListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _customerService.SelectivePaymentTermListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCustomerFormViewModel model)
        {
            model.CustomerTypes = await _customerService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync();
            model.CustomerReps = await _customerService.SelectiveCustomerRepListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _customerService.SelectivePaymentTermListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
