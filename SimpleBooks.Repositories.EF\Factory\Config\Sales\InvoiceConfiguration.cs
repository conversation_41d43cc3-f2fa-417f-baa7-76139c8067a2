﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Sales
{
    public class InvoiceConfiguration : IEntityTypeConfiguration<InvoiceModel>
    {
        public void Configure(EntityTypeBuilder<InvoiceModel> builder)
        {
            builder.<PERSON>Key(x => new { x.Id });

            builder.HasIndex(x => x.InvoiceId).IsUnique();

            builder.HasOne(d => d.Customer).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerType).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.CustomerTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerRep).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.CustomerRepId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
