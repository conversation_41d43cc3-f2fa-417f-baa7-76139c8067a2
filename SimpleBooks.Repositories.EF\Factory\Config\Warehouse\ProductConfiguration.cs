﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Warehouse
{
    public class ProductConfiguration : IEntityTypeConfiguration<ProductModel>
    {
        public void Configure(EntityTypeBuilder<ProductModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasIndex(x => x.ProductId).IsUnique();

            builder.HasOne(d => d.ProductType).WithMany(p => p.Products)
                .HasForeignKey(d => d.ProductTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.ProductCategory).WithMany(p => p.Products)
                .HasForeignKey(d => d.ProductCategoryId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
