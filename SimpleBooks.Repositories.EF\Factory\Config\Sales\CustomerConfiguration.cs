﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Sales
{
    public class CustomerConfiguration : IEntityTypeConfiguration<CustomerModel>
    {
        public void Configure(EntityTypeBuilder<CustomerModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasIndex(x => x.CustomerName).IsUnique();
            builder.HasIndex(x => x.CustomerTaxCardNumber).IsUnique();

            builder.HasOne(d => d.CustomerType).WithMany(p => p.Customers)
                .HasForeignKey(d => d.CustomerTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerRep).WithMany(p => p.Customers)
                .HasForeignKey(d => d.CustomerRepId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.Customers)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
