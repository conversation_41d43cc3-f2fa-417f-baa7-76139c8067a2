﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Sales
{
    public class InvoiceReturnConfiguration : IEntityTypeConfiguration<InvoiceReturnModel>
    {
        public void Configure(EntityTypeBuilder<InvoiceReturnModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.InvoiceReturnId).IsUnique();

            builder.HasOne(d => d.Customer).WithMany(p => p.InvoiceReturns)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerType).WithMany(p => p.InvoiceReturns)
                .HasForeignKey(d => d.CustomerTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerRep).WithMany(p => p.InvoiceReturns)
                .HasForeignKey(d => d.CustomerRepId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.InvoiceReturns)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
