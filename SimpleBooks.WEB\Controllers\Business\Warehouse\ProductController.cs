﻿namespace SimpleBooks.WEB.Controllers.Business.Warehouse
{
    public class ProductController : BaseBusinessController<
        ProductModel,
        IndexProductViewModel,
        CreateProductViewModel,
        UpdateProductViewModel,
        IndexProductFormViewModel,
        CreateProductFormViewModel,
        UpdateProductFormViewModel>
    {
        private readonly IProductService _productService;

        public ProductController(IProductService productService) : base(productService)
        {
            _productService = productService;
        }

        public override async Task<IActionResult> Create(CreateProductFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _productService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("product", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            ProductModel? entity = await _productService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateProductFormViewModel viewModel = new UpdateProductFormViewModel()
            {
                Id = entity.Id,
                ProductId = entity.ProductId,
                ProductName = entity.ProductName,
                ProductPurchasesDescription = entity.ProductPurchasesDescription,
                ProductSalesDescription = entity.ProductSalesDescription,
                ProductTypeId = entity.ProductTypeId,
                ProductCategoryId = entity.ProductCategoryId,
                ProductUnits = entity.ProductUnits.Select(x => x.ToUpdateDto()).ToList(),
                ProductTaxes = entity.ProductTaxes.Select(x => x.ToUpdateDto()).ToList(),
                ProductTypes = await _productService.SelectiveProductTypeListAsync().ToSelectListItemAsync(),
                ProductCategories = await _productService.SelectiveProductCategoryListAsync().ToSelectListItemAsync(),
                SelectiveProductUnits = await _productService.SelectiveUnitListAsync().ToSelectListItemAsync(),
                TaxTypes = await _productService.TaxTypeListAsync().GetDataOrThrowIfNullAsync(),
                TaxSubTypes = await _productService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateProductFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _productService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("product", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateProductViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateProductViewModel> createEntites = new List<CreateProductViewModel>();
            var createEntitesDict = new Dictionary<string, CreateProductViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(3).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateProductViewModel
                    {
                        ProductTypeId = ValidateValue.ValidateUlid(row.Cell(1).GetString()),
                        ProductCategoryId = ValidateValue.ValidateUlid(row.Cell(2).GetString()),
                        ProductId = mainKey,
                        ProductName = row.Cell(4).GetString(),
                        ProductPurchasesDescription = row.Cell(5).GetString(),
                        ProductSalesDescription = row.Cell(6).GetString(),
                        ProductUnits = new List<CreateProductUnitViewModel>(),
                        ProductTaxes = new List<CreateProductTaxViewModel>()
                    };

                    createEntitesDict[mainKey] = existingKey;
                }

                // Add unit if provided
                var unitId = ValidateValue.ValidateUlid(row.Cell(7).GetString());
                if (unitId != Ulid.Empty) // Optional: check if unit exists
                {
                    existingKey.ProductUnits.Add(new CreateProductUnitViewModel
                    {
                        ProductUnitId = unitId,
                        ProductUnitSalesPrice = row.Cell(8).GetValue<decimal>(),
                        ProductUnitRatio = row.Cell(9).GetValue<decimal>()
                    });
                }

                // Add tax if provided
                var taxTypeId = ValidateValue.ValidateUlid(row.Cell(10).GetString());
                if (taxTypeId != Ulid.Empty)
                {
                    existingKey.ProductTaxes.Add(new CreateProductTaxViewModel
                    {
                        TaxTypeId = taxTypeId,
                        TaxSubTypeId = ValidateValue.ValidateUlid(row.Cell(11).GetString()),
                        ProductTaxsRatio = row.Cell(12).GetValue<decimal>()
                    });
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateProductFormViewModel model)
        {
            model.ProductTypes = await _productService.SelectiveProductTypeListAsync().ToSelectListItemAsync();
            model.ProductCategories = await _productService.SelectiveProductCategoryListAsync().ToSelectListItemAsync();
            model.SelectiveProductUnits = await _productService.SelectiveUnitListAsync().ToSelectListItemAsync();
            model.TaxTypes = await _productService.TaxTypeListAsync().GetDataOrThrowIfNullAsync();
            model.TaxSubTypes = await _productService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateProductFormViewModel model)
        {
            model.ProductTypes = await _productService.SelectiveProductTypeListAsync().ToSelectListItemAsync();
            model.ProductCategories = await _productService.SelectiveProductCategoryListAsync().ToSelectListItemAsync();
            model.SelectiveProductUnits = await _productService.SelectiveUnitListAsync().ToSelectListItemAsync();
            model.TaxTypes = await _productService.TaxTypeListAsync().GetDataOrThrowIfNullAsync();
            model.TaxSubTypes = await _productService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }
    }
}
