﻿namespace SimpleBooks.Models.Model.Purchases
{
    [Table("PurchaseOrder")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<PurchaseOrderModel>))]
    public class PurchaseOrderModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Purchase Order Id")]
        public string PurchaseOrderId { get; set; }
        [CustomRequired]
        [DisplayName("Purchase Order Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly PurchaseOrderDate { get; set; }
        [CustomRequired]
        [DisplayName("Purchase Order Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly PurchaseOrderDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Vendor")]
        public Ulid VendorId { get; set; }
        public virtual VendorModel? Vendor { get; set; }

        [DisplayName("Vendor Type")]
        public Ulid? VendorTypeId { get; set; }
        public virtual VendorTypeModel? VendorType { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        [DisplayName("Document Discount Type")]
        public Ulid? DocumentDiscountTypeId { get; set; }
        public virtual DiscountTypeModel? DocumentDiscountType { get; set; }

        [DisplayName("Document Discount Rate")]
        [Range(0, 100)]
        public decimal DocumentDiscountRate { get; set; }

        [DisplayName("Document Discount Amount")]
        public decimal DocumentDiscountAmount { get; set; }

        [CustomRequired]
        [DisplayName("Purchase Order Lines")]
        public virtual ICollection<PurchaseOrderLineModel> PurchaseOrderLines { get; set; } = new List<PurchaseOrderLineModel>();
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
    }
}
