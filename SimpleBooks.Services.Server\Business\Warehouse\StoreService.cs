﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class StoreService : SimpleBooksBaseService<StoreModel, StoreModel, CreateStoreViewModel, UpdateStoreViewModel>, IStoreService
    {
        private readonly IUnitOfWork _unitOfWork;

        public StoreService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.Store)
        {
            _unitOfWork = unitOfWork;
        }

        public override void ValidateEntity(StoreModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<StoreModel> repositorySpecifications = new RepositorySpecifications<StoreModel>()
                {
                    SearchValue = x => x.StoreName == model.StoreName,
                    IsTackable = false,
                };
                var isExistingStore = _unitOfWork.Store.Get(repositorySpecifications);
                if (isExistingStore != null && isExistingStore.Id != model.Id)
                    throw new ValidationException("Store with the same Name already exists.");
            }
        }
    }
}
