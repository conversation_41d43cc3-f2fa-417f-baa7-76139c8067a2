﻿namespace SimpleBooks.WEB.Controllers.Business.Warehouse
{
    public class ProductTypeController : BaseBusinessController<
        ProductTypeModel,
        ProductTypeModel,
        CreateProductTypeViewModel,
        UpdateProductTypeViewModel,
        IndexProductTypeFormViewModel,
        CreateProductTypeFormViewModel,
        UpdateProductTypeFormViewModel>
    {
        private readonly IProductTypeService _productTypeService;

        public ProductTypeController(IProductTypeService productTypeService) : base(productTypeService)
        {
            _productTypeService = productTypeService;
        }

        public override async Task<IActionResult> Create(CreateProductTypeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _productTypeService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("productType", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            ProductTypeModel? entity = await _productTypeService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateProductTypeFormViewModel viewModel = new UpdateProductTypeFormViewModel()
            {
                Id = entity.Id,
                ProductTypeName = entity.ProductTypeName,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateProductTypeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _productTypeService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("productType", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateProductTypeViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateProductTypeViewModel> createEntites = new List<CreateProductTypeViewModel>();
            var createEntitesDict = new Dictionary<string, CreateProductTypeViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateProductTypeViewModel
                    {
                        ProductTypeName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateProductTypeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateProductTypeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
