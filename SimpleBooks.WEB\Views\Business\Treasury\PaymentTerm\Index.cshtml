﻿@model IndexPaymentTermFormViewModel

@{
    ViewData["Title"] = "PaymentTerms";
    List<PaymentTermModel> PaymentTerms = Model.MainList.Items;
}

<div class="row d-flex">
    <div class="me-3 pb-2">
        <a class="btn btn-secondary btn-lg float-start" asp-action="Create" method="get">
            <i class="bi bi-plus-circle-dotted"></i>
            Add PaymentTerm
        </a>
        <a class="btn btn-success btn-lg float-end" asp-action="ExportToExcel">
            <i class="bi bi-file-earmark-spreadsheet"></i>
            Export To Excel
        </a>
    </div>
    <form class="d-flex align-items-center" asp-action="Index">
        <input class="form-control me-3" type="search" asp-for="SearchValue" placeholder="Search">
        <button class="btn btn-info btn-lg" type="submit">Search</button>
    </form>
</div>

@if (!PaymentTerms.Any())
{
    <div class="alert alert-warning mt-5">
        <h4 class="alert-heading">No Payment Terms!</h4>
        <p class="mb-0">No Payment Terms were added yet.</p>
    </div>
}
else
{
    <table class="table table-hover">
        <thead>
            <tr>
                <th scope="col">Payment Term Name</th>
                <th scope="col" class="">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (PaymentTermModel PaymentTerm in PaymentTerms)
            {
                <tr>
                    <td>@PaymentTerm.PaymentTermName</td>

                    <td class="overflow-hidden align-middle">
                        <div class="d-flex justify-content-end">
                            <a class="btn btn-info rounded rounded-3 me-2" asp-action="Update" asp-route-id="@PaymentTerm.Id">
                                <i class="bi bi-pencil-fill"></i>
                            </a>
                            <a href="javascript:;" class="btn btn-danger rounded rounded-3 js-delete" data-id="@PaymentTerm.Id">
                                <i class="bi bi-trash3"></i>
                            </a>
                        </div>
                    </td>

                </tr>
            }
        </tbody>
    </table>

    if (Model != null && Model.MainList != null)
        @await Html.PartialAsync("_Pagination", Model)
}

@section Scripts
{
    <script src="~/js/PaymentTerm-index.js" asp-append-version="true"></script>
}