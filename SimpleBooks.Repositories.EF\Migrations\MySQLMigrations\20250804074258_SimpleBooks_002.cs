﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_002 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "VendorTypeName",
                table: "VendorType",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "VendorName",
                table: "Vendor",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "StoreName",
                table: "Store",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "SalesOrderId",
                table: "SalesOrder",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "PurchaseOrderId",
                table: "PurchaseOrder",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ProductTypeName",
                table: "ProductType",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ProductCategoryName",
                table: "ProductCategory",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ProductId",
                table: "Product",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "PaymentTermName",
                table: "PaymentTerm",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "InvoiceReturnId",
                table: "InvoiceReturn",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "InvoiceId",
                table: "Invoice",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ExpensesName",
                table: "Expenses",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "EmployeeName",
                table: "Employee",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "DrawerName",
                table: "Drawer",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerTypeName",
                table: "CustomerType",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "Customer",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CheckVaultName",
                table: "CheckVault",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "BillReturnId",
                table: "BillReturn",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "BillId",
                table: "Bill",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "BankName",
                table: "Bank",
                type: "varchar(255)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_VendorType_VendorTypeName",
                table: "VendorType",
                column: "VendorTypeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Vendor_VendorName",
                table: "Vendor",
                column: "VendorName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Vendor_VendorTaxCardNumber",
                table: "Vendor",
                column: "VendorTaxCardNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_User_UserName",
                table: "User",
                column: "UserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Store_StoreName",
                table: "Store",
                column: "StoreName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_SalesOrderId",
                table: "SalesOrder",
                column: "SalesOrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_PurchaseOrderId",
                table: "PurchaseOrder",
                column: "PurchaseOrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductType_ProductTypeName",
                table: "ProductType",
                column: "ProductTypeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductCategory_ProductCategoryName",
                table: "ProductCategory",
                column: "ProductCategoryName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Product_ProductId",
                table: "Product",
                column: "ProductId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PaymentTerm_PaymentTermName",
                table: "PaymentTerm",
                column: "PaymentTermName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReturn_InvoiceReturnId",
                table: "InvoiceReturn",
                column: "InvoiceReturnId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Invoice_InvoiceId",
                table: "Invoice",
                column: "InvoiceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Expenses_ExpensesName",
                table: "Expenses",
                column: "ExpensesName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Employee_EmployeeName",
                table: "Employee",
                column: "EmployeeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Drawer_DrawerName",
                table: "Drawer",
                column: "DrawerName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerType_CustomerTypeName",
                table: "CustomerType",
                column: "CustomerTypeName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Customer_CustomerName",
                table: "Customer",
                column: "CustomerName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Customer_CustomerTaxCardNumber",
                table: "Customer",
                column: "CustomerTaxCardNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CheckVault_CheckVaultName",
                table: "CheckVault",
                column: "CheckVaultName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BillReturn_BillReturnId",
                table: "BillReturn",
                column: "BillReturnId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Bill_BillId",
                table: "Bill",
                column: "BillId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Bank_BankName",
                table: "Bank",
                column: "BankName",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_VendorType_VendorTypeName",
                table: "VendorType");

            migrationBuilder.DropIndex(
                name: "IX_Vendor_VendorName",
                table: "Vendor");

            migrationBuilder.DropIndex(
                name: "IX_Vendor_VendorTaxCardNumber",
                table: "Vendor");

            migrationBuilder.DropIndex(
                name: "IX_User_UserName",
                table: "User");

            migrationBuilder.DropIndex(
                name: "IX_Store_StoreName",
                table: "Store");

            migrationBuilder.DropIndex(
                name: "IX_SalesOrder_SalesOrderId",
                table: "SalesOrder");

            migrationBuilder.DropIndex(
                name: "IX_PurchaseOrder_PurchaseOrderId",
                table: "PurchaseOrder");

            migrationBuilder.DropIndex(
                name: "IX_ProductType_ProductTypeName",
                table: "ProductType");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategory_ProductCategoryName",
                table: "ProductCategory");

            migrationBuilder.DropIndex(
                name: "IX_Product_ProductId",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_PaymentTerm_PaymentTermName",
                table: "PaymentTerm");

            migrationBuilder.DropIndex(
                name: "IX_InvoiceReturn_InvoiceReturnId",
                table: "InvoiceReturn");

            migrationBuilder.DropIndex(
                name: "IX_Invoice_InvoiceId",
                table: "Invoice");

            migrationBuilder.DropIndex(
                name: "IX_Expenses_ExpensesName",
                table: "Expenses");

            migrationBuilder.DropIndex(
                name: "IX_Employee_EmployeeName",
                table: "Employee");

            migrationBuilder.DropIndex(
                name: "IX_Drawer_DrawerName",
                table: "Drawer");

            migrationBuilder.DropIndex(
                name: "IX_CustomerType_CustomerTypeName",
                table: "CustomerType");

            migrationBuilder.DropIndex(
                name: "IX_Customer_CustomerName",
                table: "Customer");

            migrationBuilder.DropIndex(
                name: "IX_Customer_CustomerTaxCardNumber",
                table: "Customer");

            migrationBuilder.DropIndex(
                name: "IX_CheckVault_CheckVaultName",
                table: "CheckVault");

            migrationBuilder.DropIndex(
                name: "IX_BillReturn_BillReturnId",
                table: "BillReturn");

            migrationBuilder.DropIndex(
                name: "IX_Bill_BillId",
                table: "Bill");

            migrationBuilder.DropIndex(
                name: "IX_Bank_BankName",
                table: "Bank");

            migrationBuilder.AlterColumn<string>(
                name: "VendorTypeName",
                table: "VendorType",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "VendorName",
                table: "Vendor",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "StoreName",
                table: "Store",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "SalesOrderId",
                table: "SalesOrder",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "PurchaseOrderId",
                table: "PurchaseOrder",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ProductTypeName",
                table: "ProductType",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ProductCategoryName",
                table: "ProductCategory",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ProductId",
                table: "Product",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "PaymentTermName",
                table: "PaymentTerm",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "InvoiceReturnId",
                table: "InvoiceReturn",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "InvoiceId",
                table: "Invoice",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "ExpensesName",
                table: "Expenses",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "EmployeeName",
                table: "Employee",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "DrawerName",
                table: "Drawer",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerTypeName",
                table: "CustomerType",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "Customer",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "CheckVaultName",
                table: "CheckVault",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "BillReturnId",
                table: "BillReturn",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "BillId",
                table: "Bill",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "BankName",
                table: "Bank",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
