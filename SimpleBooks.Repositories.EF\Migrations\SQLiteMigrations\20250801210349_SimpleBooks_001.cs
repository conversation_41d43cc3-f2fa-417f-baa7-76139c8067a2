﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_001 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Audit",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Type = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    TableName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    DateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    OldValues = table.Column<string>(type: "TEXT", nullable: false),
                    NewValues = table.Column<string>(type: "TEXT", nullable: false),
                    AffectedColumns = table.Column<string>(type: "TEXT", nullable: false),
                    PrimaryKey = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Audit", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Bank",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    BankName = table.Column<string>(type: "TEXT", nullable: false),
                    BankIdentifier = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Bank", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BeneficiaryType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    BeneficiaryTypeName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CheckClear",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ClearDate = table.Column<DateTime>(type: "Date", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckClear", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CheckCollection",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CollectionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckCollection", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CheckReject",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    RejectDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RejectReason = table.Column<string>(type: "TEXT", nullable: false),
                    RefranceNumber = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckReject", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CheckStatus",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CheckStatusName = table.Column<string>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckStatus", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CheckVault",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CheckVaultName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckVault", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CustomerTypeName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Drawer",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    DrawerName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Drawer", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Expenses",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ExpensesName = table.Column<string>(type: "TEXT", nullable: false),
                    ExpensesDescription = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Expenses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PaymentTerm",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    PaymentTermName = table.Column<string>(type: "TEXT", nullable: false),
                    DiscountPercentage = table.Column<int>(type: "INTEGER", nullable: false),
                    Discriminator = table.Column<string>(type: "TEXT", maxLength: 34, nullable: false),
                    NetDueBeforeDayOfMonth = table.Column<int>(type: "INTEGER", nullable: true),
                    DueNextMonthWithinDays = table.Column<int>(type: "INTEGER", nullable: true),
                    IfPaidBeforeDayOfMonth = table.Column<int>(type: "INTEGER", nullable: true),
                    NetDueDays = table.Column<int>(type: "INTEGER", nullable: true),
                    IfPaidWithinDays = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentTerm", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProductCategory",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ProductCategoryName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductCategory", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProductType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ProductTypeName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Store",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    StoreName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Store", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaxType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Code = table.Column<string>(type: "TEXT", nullable: false),
                    Desc_en = table.Column<string>(type: "TEXT", nullable: false),
                    Desc_ar = table.Column<string>(type: "TEXT", nullable: false),
                    IsAddition = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TransactionType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    TransactionTypeName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Unit",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    UnitName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Unit", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VendorType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    VendorTypeName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VendorType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BankAccount",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    BankAccountNumber = table.Column<string>(type: "TEXT", nullable: false),
                    BankAccountCurrency = table.Column<string>(type: "TEXT", nullable: false),
                    BankAccountIBAN = table.Column<string>(type: "TEXT", nullable: true),
                    BankAccountSwiftCode = table.Column<string>(type: "TEXT", nullable: true),
                    BankId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BankAccount", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BankAccount_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CheckVaultLocation",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CheckVaultLocationNumber = table.Column<string>(type: "TEXT", nullable: false),
                    CheckVaultLocationCurrency = table.Column<string>(type: "TEXT", nullable: false),
                    CheckVaultId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckVaultLocation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckVaultLocation_CheckVault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "CheckVault",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DrawerLocation",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    DrawerLocationNumber = table.Column<string>(type: "TEXT", nullable: false),
                    DrawerLocationCurrency = table.Column<string>(type: "TEXT", nullable: false),
                    DrawerId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DrawerLocation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DrawerLocation_Drawer_DrawerId",
                        column: x => x.DrawerId,
                        principalTable: "Drawer",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TaxSubType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Code = table.Column<string>(type: "TEXT", nullable: false),
                    Desc_en = table.Column<string>(type: "TEXT", nullable: false),
                    Desc_ar = table.Column<string>(type: "TEXT", nullable: false),
                    TaxTypeId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxSubType", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxSubType_TaxType_TaxTypeId",
                        column: x => x.TaxTypeId,
                        principalTable: "TaxType",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Product",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ProductId = table.Column<string>(type: "TEXT", nullable: false),
                    ProductName = table.Column<string>(type: "TEXT", nullable: false),
                    ProductPurchasesDescription = table.Column<string>(type: "TEXT", nullable: false),
                    ProductSalesDescription = table.Column<string>(type: "TEXT", nullable: false),
                    ProductTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    ProductCategoryId = table.Column<string>(type: "TEXT", nullable: true),
                    UnitModelId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Product", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Product_ProductCategory_ProductCategoryId",
                        column: x => x.ProductCategoryId,
                        principalTable: "ProductCategory",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Product_ProductType_ProductTypeId",
                        column: x => x.ProductTypeId,
                        principalTable: "ProductType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Product_Unit_UnitModelId",
                        column: x => x.UnitModelId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Vendor",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    VendorName = table.Column<string>(type: "TEXT", nullable: false),
                    VendorTaxCardNumber = table.Column<string>(type: "TEXT", maxLength: 9, nullable: false),
                    VendorTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Vendor", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Vendor_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Vendor_VendorType_VendorTypeId",
                        column: x => x.VendorTypeId,
                        principalTable: "VendorType",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CheckDeposit",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    DepositDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RefranceNumber = table.Column<string>(type: "TEXT", nullable: true),
                    BankId = table.Column<string>(type: "TEXT", nullable: false),
                    BankAccountId = table.Column<string>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckDeposit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckDeposit_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckDeposit_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CheckReturn",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ReturnDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RefranceNumber = table.Column<string>(type: "TEXT", nullable: true),
                    CheckVaultId = table.Column<string>(type: "TEXT", nullable: false),
                    CheckVaultLocationId = table.Column<string>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckReturn", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckReturn_CheckVaultLocation_CheckVaultLocationId",
                        column: x => x.CheckVaultLocationId,
                        principalTable: "CheckVaultLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckReturn_CheckVault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "CheckVault",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "ProductTax",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ProductTaxsRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductId = table.Column<string>(type: "TEXT", nullable: false),
                    TaxTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    TaxSubTypeId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductTax", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductTax_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ProductTax_TaxSubType_TaxSubTypeId",
                        column: x => x.TaxSubTypeId,
                        principalTable: "TaxSubType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ProductTax_TaxType_TaxTypeId",
                        column: x => x.TaxTypeId,
                        principalTable: "TaxType",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "ProductUnit",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ProductUnitSalesPrice = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductUnitRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductId = table.Column<string>(type: "TEXT", nullable: false),
                    ProductUnitId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductUnit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductUnit_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ProductUnit_Unit_ProductUnitId",
                        column: x => x.ProductUnitId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Bill",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    BillId = table.Column<string>(type: "TEXT", nullable: false),
                    BillDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    BillDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    VendorId = table.Column<string>(type: "TEXT", nullable: false),
                    VendorTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Bill", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Bill_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Bill_VendorType_VendorTypeId",
                        column: x => x.VendorTypeId,
                        principalTable: "VendorType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Bill_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "BillReturn",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    BillReturnId = table.Column<string>(type: "TEXT", nullable: false),
                    BillReturnDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    BillReturnDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    VendorId = table.Column<string>(type: "TEXT", nullable: false),
                    VendorTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BillReturn", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BillReturn_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BillReturn_VendorType_VendorTypeId",
                        column: x => x.VendorTypeId,
                        principalTable: "VendorType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BillReturn_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PurchaseOrder",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    PurchaseOrderId = table.Column<string>(type: "TEXT", nullable: false),
                    PurchaseOrderDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    PurchaseOrderDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    VendorId = table.Column<string>(type: "TEXT", nullable: false),
                    VendorTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseOrder_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrder_VendorType_VendorTypeId",
                        column: x => x.VendorTypeId,
                        principalTable: "VendorType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrder_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PurchaseOrderLine",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Quantity = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Price = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    UnitQtyRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductId = table.Column<string>(type: "TEXT", nullable: false),
                    ProductUnitId = table.Column<string>(type: "TEXT", nullable: false),
                    PurchaseOrderId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrderLine", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLine_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLine_PurchaseOrder_PurchaseOrderId",
                        column: x => x.PurchaseOrderId,
                        principalTable: "PurchaseOrder",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLine_Unit_ProductUnitId",
                        column: x => x.ProductUnitId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "BankTransferTreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    BankId = table.Column<string>(type: "TEXT", nullable: false),
                    BankAccountId = table.Column<string>(type: "TEXT", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "INTEGER", nullable: false),
                    TVID = table.Column<int>(type: "INTEGER", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Note = table.Column<string>(type: "TEXT", nullable: false),
                    RefranceNumber = table.Column<string>(type: "TEXT", nullable: true),
                    TransactionTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    BeneficiaryTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    VendorId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerId = table.Column<string>(type: "TEXT", nullable: true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BankTransferTreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                        column: x => x.BeneficiaryTypeId,
                        principalTable: "BeneficiaryType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CashTreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    DrawerId = table.Column<string>(type: "TEXT", nullable: false),
                    DrawerLocationId = table.Column<string>(type: "TEXT", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "INTEGER", nullable: false),
                    TVID = table.Column<int>(type: "INTEGER", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Note = table.Column<string>(type: "TEXT", nullable: false),
                    RefranceNumber = table.Column<string>(type: "TEXT", nullable: true),
                    TransactionTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    BeneficiaryTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    VendorId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerId = table.Column<string>(type: "TEXT", nullable: true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CashTreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                        column: x => x.BeneficiaryTypeId,
                        principalTable: "BeneficiaryType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_DrawerLocation_DrawerLocationId",
                        column: x => x.DrawerLocationId,
                        principalTable: "DrawerLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_Drawer_DrawerId",
                        column: x => x.DrawerId,
                        principalTable: "Drawer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CheckStatusHistory",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    Note = table.Column<string>(type: "TEXT", nullable: false),
                    CheckStatusFromId = table.Column<string>(type: "TEXT", nullable: false),
                    CheckStatusToId = table.Column<string>(type: "TEXT", nullable: false),
                    CheckTreasuryVoucherId = table.Column<string>(type: "TEXT", nullable: false),
                    CheckDepositId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckCollectionId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckRejectId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckReturnId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckClearId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckStatusHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckClear_CheckClearId",
                        column: x => x.CheckClearId,
                        principalTable: "CheckClear",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckCollection_CheckCollectionId",
                        column: x => x.CheckCollectionId,
                        principalTable: "CheckCollection",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckDeposit_CheckDepositId",
                        column: x => x.CheckDepositId,
                        principalTable: "CheckDeposit",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckReject_CheckRejectId",
                        column: x => x.CheckRejectId,
                        principalTable: "CheckReject",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckReturn_CheckReturnId",
                        column: x => x.CheckReturnId,
                        principalTable: "CheckReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckStatus_CheckStatusFromId",
                        column: x => x.CheckStatusFromId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckStatus_CheckStatusToId",
                        column: x => x.CheckStatusToId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CheckTreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CheckNumber = table.Column<string>(type: "TEXT", nullable: false),
                    DueDate = table.Column<DateTime>(type: "Date", nullable: false),
                    IssuerName = table.Column<string>(type: "TEXT", nullable: false),
                    BearerName = table.Column<string>(type: "TEXT", nullable: false),
                    CheckStatusId = table.Column<string>(type: "TEXT", nullable: false),
                    CheckVaultId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckVaultLocationId = table.Column<string>(type: "TEXT", nullable: true),
                    BankId = table.Column<string>(type: "TEXT", nullable: true),
                    BankAccountId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckDepositId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckCollectionId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckRejectId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckReturnId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckClearId = table.Column<string>(type: "TEXT", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "INTEGER", nullable: false),
                    TVID = table.Column<int>(type: "INTEGER", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Note = table.Column<string>(type: "TEXT", nullable: false),
                    RefranceNumber = table.Column<string>(type: "TEXT", nullable: true),
                    TransactionTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    BeneficiaryTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    VendorId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerId = table.Column<string>(type: "TEXT", nullable: true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckTreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                        column: x => x.BeneficiaryTypeId,
                        principalTable: "BeneficiaryType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckClear_CheckClearId",
                        column: x => x.CheckClearId,
                        principalTable: "CheckClear",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckCollection_CheckCollectionId",
                        column: x => x.CheckCollectionId,
                        principalTable: "CheckCollection",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckDeposit_CheckDepositId",
                        column: x => x.CheckDepositId,
                        principalTable: "CheckDeposit",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckReject_CheckRejectId",
                        column: x => x.CheckRejectId,
                        principalTable: "CheckReject",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckReturn_CheckReturnId",
                        column: x => x.CheckReturnId,
                        principalTable: "CheckReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckStatus_CheckStatusId",
                        column: x => x.CheckStatusId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckVaultLocation_CheckVaultLocationId",
                        column: x => x.CheckVaultLocationId,
                        principalTable: "CheckVaultLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckVault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "CheckVault",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Customer",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    CustomerName = table.Column<string>(type: "TEXT", nullable: false),
                    CustomerTaxCardNumber = table.Column<string>(type: "TEXT", maxLength: 9, nullable: false),
                    CustomerTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerRepId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Customer_CustomerType_CustomerTypeId",
                        column: x => x.CustomerTypeId,
                        principalTable: "CustomerType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Customer_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Employee",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    EmployeeName = table.Column<string>(type: "TEXT", nullable: false),
                    EmployeeEMail = table.Column<string>(type: "TEXT", nullable: false),
                    EmployeePhone = table.Column<string>(type: "TEXT", nullable: false),
                    EmployeeIsRep = table.Column<bool>(type: "INTEGER", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Employee", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Invoice",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    InvoiceId = table.Column<string>(type: "TEXT", nullable: false),
                    InvoiceDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    InvoiceDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    CustomerId = table.Column<string>(type: "TEXT", nullable: false),
                    CustomerTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerRepId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoice", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Invoice_CustomerType_CustomerTypeId",
                        column: x => x.CustomerTypeId,
                        principalTable: "CustomerType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Invoice_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Invoice_Employee_CustomerRepId",
                        column: x => x.CustomerRepId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Invoice_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "InvoiceReturn",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    InvoiceReturnId = table.Column<string>(type: "TEXT", nullable: false),
                    InvoiceReturnDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    InvoiceReturnDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    CustomerId = table.Column<string>(type: "TEXT", nullable: false),
                    CustomerTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerRepId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceReturn", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoiceReturn_CustomerType_CustomerTypeId",
                        column: x => x.CustomerTypeId,
                        principalTable: "CustomerType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InvoiceReturn_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InvoiceReturn_Employee_CustomerRepId",
                        column: x => x.CustomerRepId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InvoiceReturn_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesOrder",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    SalesOrderId = table.Column<string>(type: "TEXT", nullable: false),
                    SalesOrderDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    SalesOrderDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    CustomerId = table.Column<string>(type: "TEXT", nullable: false),
                    CustomerTypeId = table.Column<string>(type: "TEXT", nullable: true),
                    CustomerRepId = table.Column<string>(type: "TEXT", nullable: true),
                    PaymentTermId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesOrder_CustomerType_CustomerTypeId",
                        column: x => x.CustomerTypeId,
                        principalTable: "CustomerType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrder_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrder_Employee_CustomerRepId",
                        column: x => x.CustomerRepId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrder_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TreasuryLine",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    TreasuryLineTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    ExpensesId = table.Column<string>(type: "TEXT", nullable: true),
                    BillId = table.Column<string>(type: "TEXT", nullable: true),
                    BillReturnId = table.Column<string>(type: "TEXT", nullable: true),
                    InvoiceId = table.Column<string>(type: "TEXT", nullable: true),
                    InvoiceReturnId = table.Column<string>(type: "TEXT", nullable: true),
                    CashTreasuryVoucherId = table.Column<string>(type: "TEXT", nullable: true),
                    CheckTreasuryVoucherId = table.Column<string>(type: "TEXT", nullable: true),
                    BankTransferTreasuryVoucherId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TreasuryLine", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TreasuryLine_BankTransferTreasuryVoucher_BankTransferTreasuryVoucherId",
                        column: x => x.BankTransferTreasuryVoucherId,
                        principalTable: "BankTransferTreasuryVoucher",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_BillReturn_BillReturnId",
                        column: x => x.BillReturnId,
                        principalTable: "BillReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_Bill_BillId",
                        column: x => x.BillId,
                        principalTable: "Bill",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_CashTreasuryVoucher_CashTreasuryVoucherId",
                        column: x => x.CashTreasuryVoucherId,
                        principalTable: "CashTreasuryVoucher",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                        column: x => x.CheckTreasuryVoucherId,
                        principalTable: "CheckTreasuryVoucher",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_Expenses_ExpensesId",
                        column: x => x.ExpensesId,
                        principalTable: "Expenses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_InvoiceReturn_InvoiceReturnId",
                        column: x => x.InvoiceReturnId,
                        principalTable: "InvoiceReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryLine_Invoice_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "Invoice",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesOrderLine",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Quantity = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Price = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    UnitQtyRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductId = table.Column<string>(type: "TEXT", nullable: false),
                    ProductUnitId = table.Column<string>(type: "TEXT", nullable: false),
                    SalesOrderId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesOrderLine", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesOrderLine_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrderLine_SalesOrder_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalTable: "SalesOrder",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrderLine_Unit_ProductUnitId",
                        column: x => x.ProductUnitId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Inventory",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Quantity = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    CostPrice = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    CostAmount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    SalesPrice = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    SalesAmount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    UnitQtyRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    TaxAmount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    NetAmount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    TransactionTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    ProductId = table.Column<string>(type: "TEXT", nullable: false),
                    ProductUnitId = table.Column<string>(type: "TEXT", nullable: false),
                    StoreId = table.Column<string>(type: "TEXT", nullable: false),
                    BillId = table.Column<string>(type: "TEXT", nullable: true),
                    BillReturnId = table.Column<string>(type: "TEXT", nullable: true),
                    PurchaseOrderId = table.Column<string>(type: "TEXT", nullable: true),
                    PurchaseOrderLineId = table.Column<string>(type: "TEXT", nullable: true),
                    InvoiceId = table.Column<string>(type: "TEXT", nullable: true),
                    InvoiceReturnId = table.Column<string>(type: "TEXT", nullable: true),
                    SalesOrderId = table.Column<string>(type: "TEXT", nullable: true),
                    SalesOrderLineId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Inventory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Inventory_BillReturn_BillReturnId",
                        column: x => x.BillReturnId,
                        principalTable: "BillReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_Bill_BillId",
                        column: x => x.BillId,
                        principalTable: "Bill",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_InvoiceReturn_InvoiceReturnId",
                        column: x => x.InvoiceReturnId,
                        principalTable: "InvoiceReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_Invoice_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "Invoice",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_PurchaseOrderLine_PurchaseOrderLineId",
                        column: x => x.PurchaseOrderLineId,
                        principalTable: "PurchaseOrderLine",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_PurchaseOrder_PurchaseOrderId",
                        column: x => x.PurchaseOrderId,
                        principalTable: "PurchaseOrder",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_SalesOrderLine_SalesOrderLineId",
                        column: x => x.SalesOrderLineId,
                        principalTable: "SalesOrderLine",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_SalesOrder_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalTable: "SalesOrder",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_Store_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Store",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Inventory_Unit_ProductUnitId",
                        column: x => x.ProductUnitId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "InventoryProductTax",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    InventoryTaxsRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    InventoryTaxsAmount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    InventoryId = table.Column<string>(type: "TEXT", nullable: false),
                    TaxTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    TaxSubTypeId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryProductTax", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InventoryProductTax_Inventory_InventoryId",
                        column: x => x.InventoryId,
                        principalTable: "Inventory",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InventoryProductTax_TaxSubType_TaxSubTypeId",
                        column: x => x.TaxSubTypeId,
                        principalTable: "TaxSubType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InventoryProductTax_TaxType_TaxTypeId",
                        column: x => x.TaxTypeId,
                        principalTable: "TaxType",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RefreshTokens",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Token = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ExpiresOn = table.Column<DateTime>(type: "TEXT", nullable: false),
                    RevokedOn = table.Column<DateTime>(type: "TEXT", nullable: true),
                    UserId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RefreshTokens", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ScreensAccessProfile",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    UserModelId = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    ScreensAccessProfileName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScreensAccessProfile", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ScreensAccessProfileDetails",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ScreensAccessProfileId = table.Column<string>(type: "TEXT", nullable: false),
                    ScreenId = table.Column<string>(type: "TEXT", nullable: false),
                    CanShow = table.Column<bool>(type: "INTEGER", nullable: false),
                    CanOpen = table.Column<bool>(type: "INTEGER", nullable: false),
                    CanAdd = table.Column<bool>(type: "INTEGER", nullable: false),
                    CanEdit = table.Column<bool>(type: "INTEGER", nullable: false),
                    CanDelete = table.Column<bool>(type: "INTEGER", nullable: false),
                    CanPrint = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScreensAccessProfileDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ScreensAccessProfileDetails_ScreensAccessProfile_ScreensAccessProfileId",
                        column: x => x.ScreensAccessProfileId,
                        principalTable: "ScreensAccessProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "User",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: false),
                    SettingId = table.Column<string>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    UserName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Password = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    UserTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    ScreensAccessProfileId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_User", x => x.Id);
                    table.ForeignKey(
                        name: "FK_User_ScreensAccessProfile_ScreensAccessProfileId",
                        column: x => x.ScreensAccessProfileId,
                        principalTable: "ScreensAccessProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Setting",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<string>(type: "TEXT", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Setting", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Setting_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.InsertData(
                table: "BeneficiaryType",
                columns: new[] { "Id", "BeneficiaryTypeName" },
                values: new object[,]
                {
                    { "01JTKF9QSYN1TR6DMBY0MZ3BY6", "Vendor" },
                    { "01JTKF9ZC7W32GQAFRSVP00D47", "Customer" },
                    { "01JTKFA29V6V59R8C3DYMX90M3", "Employee" }
                });

            migrationBuilder.InsertData(
                table: "CheckStatus",
                columns: new[] { "Id", "CheckStatusName", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "IsActive", "ModifaiedAt", "ModifaiedBy" },
                values: new object[,]
                {
                    { "01JZBRJWHRNQTHR1MJ693B2STZ", "Issued", null, null, null, null, true, null, null },
                    { "01JZBRK149KQWD0TJ971CQ5WC1", "Received", null, null, null, null, true, null, null },
                    { "01JZBRK3ZGQPTB0S7RNN8QC8EA", "Deposited", null, null, null, null, true, null, null },
                    { "01JZBRK6P4GNTX4097YAKT3ZRZ", "Cleared", null, null, null, null, true, null, null },
                    { "01JZBRKA75CZZW5P4ARW47NY4P", "Returned", null, null, null, null, true, null, null },
                    { "01JZBRKD187QRR8QSB7SWJQ4TM", "Rejected", null, null, null, null, true, null, null },
                    { "01JZXN7EBKTG5YX238JNT92KFD", "Unknown", null, null, null, null, true, null, null },
                    { "01K0J1KKN1YZMJYQ86F7C4JMS4", "Collected", null, null, null, null, true, null, null }
                });

            migrationBuilder.InsertData(
                table: "ScreensAccessProfile",
                columns: new[] { "Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "IsActive", "ModifaiedAt", "ModifaiedBy", "ScreensAccessProfileName", "UserModelId" },
                values: new object[,]
                {
                    { "01JVDCZD2ST67FD5DFX2KECWKR", null, null, null, null, true, null, null, "Admin Profile", null },
                    { "01JVDCZHG4FZWV2XFZ4EEYJN3Z", null, null, null, null, true, null, null, "User Profile", null }
                });

            migrationBuilder.InsertData(
                table: "TaxType",
                columns: new[] { "Id", "Code", "Desc_ar", "Desc_en", "IsAddition" },
                values: new object[,]
                {
                    { "01JRN6KEMGHX79ZW8H9GPDEF72", "T1", "ضريبه القيمه المضافه", "Value added tax", true },
                    { "01JRN6KEMHMNRPK8CTMAZ3XQTK", "T4", "الخصم تحت حساب الضريبه", "Withholding tax (WHT)", false },
                    { "01JRN6KEMHQ1MEF9FBCDT2DA5S", "T3", "ضريبه الجدول (قطعيه)", "Table tax (Fixed Amount)", true },
                    { "01JRN6KEMHT2B8G7NZVX6J0B7W", "T2", "ضريبه الجدول (نسبيه)", "Table tax (percentage)", true },
                    { "01JRN6KEMHVHVT0JR971YVPJ92", "T5", "ضريبه الدمغه (نسبيه)", "Stamping tax (percentage)", true },
                    { "01JRN6KEMJ1EXMTWFQQ8BYP7RS", "T10", "رسم المحليات", "Municipality Fees", true },
                    { "01JRN6KEMJ1FATNAQ3540BR73H", "T6", "ضريبه الدمغه (قطعيه بمقدار ثابت )", "Stamping Tax (amount)", true },
                    { "01JRN6KEMJ36AZH3TSZM1QXXQ2", "T11", "رسم التامين الصحى", "Medical insurance fee", true },
                    { "01JRN6KEMJ4X5C3NYGDDSZEGEM", "T12", "رسوم أخري", "Other fees", true },
                    { "01JRN6KEMJ7CWC2EZBRBKD2RFJ", "T8", "رسم تنميه الموارد", "Resource development fee", true },
                    { "01JRN6KEMJCN4J6XCNJGBCSH9D", "T7", "ضريبة الملاهى", "Entertainment tax", true },
                    { "01JRN6KEMJHNG5Q0R5AMHSBV85", "T9", "رسم خدمة", "Table tax (percentage)", true },
                    { "01JRN6R5RW3G72VRJ4Q2CA2JTV", "T14", "ضريبه الدمغه (قطعيه بمقدار ثابت )", "Stamping Tax (amount)", true },
                    { "01JRN6R5RW7PD5HBF3563V3JN7", "T16", "رسم تنميه الموارد", "Resource development fee", true },
                    { "01JRN6R5RWB9NB0XFSY0G9VG4S", "T18", "رسم المحليات", "Municipality Fees", true },
                    { "01JRN6R5RWET9R49NR7QTZA801", "T19", "رسم التامين الصحى", "Medical insurance fee", true },
                    { "01JRN6R5RWFYWMR74SJCX2G4XN", "T17", "رسم خدمة", "Table tax (percentage)", true },
                    { "01JRN6R5RWG7KPJMKBDAHZVHDH", "T15", "ضريبة الملاهى", "Entertainment tax", true },
                    { "01JRN6R5RWNEB4PMWHKDY8KEEW", "T20", "رسوم أخرى", "Other fees", true },
                    { "01JRN6R5RWY935F174XQM19Y1T", "T13", "ضريبه الدمغه (نسبيه)", "Stamping tax (percentage)", true }
                });

            migrationBuilder.InsertData(
                table: "TransactionType",
                columns: new[] { "Id", "TransactionTypeName" },
                values: new object[,]
                {
                    { "01JRKA5XSNHEQWKTR0NCJWYC6W", "Bill" },
                    { "01JRKA5XSQ45QDM1C7H1TME01A", "Bill Return" },
                    { "01JRKA5XSQ95F5J1D2DBHHM375", "Inventory Adjustment" },
                    { "01JRKA5XSQGDQ36JWP7BFAE06R", "Invoice Return" },
                    { "01JRKA5XSQSFE4H3R3M177EDN5", "Invoice" },
                    { "01JRKA5XSR7RW0JNTPY9X6JPBV", "Inventory Transfer" },
                    { "01JRKA5XSRKZCHG3CMFM9ZPRN7", "Inventory Opening Balance" },
                    { "01JRKA5XSRQHRCBY9A06M2J1RQ", "SalesOrder" },
                    { "01JZBSNG4F3P7EYRA6T1JY5HPC", "Cash In" },
                    { "01JZBSNQWAHRA4CK6TKK9SVY43", "Cash Out" },
                    { "01JZBSNVFRKWDYN58VC8Y5CPXM", "Bank In" },
                    { "01JZBSP0S7B0C5T3YXWJ2WSGPP", "Bank Out" },
                    { "01JZBSP3PFVH37SZ8D5DEQF304", "Check In" },
                    { "01JZBSP7B64B1GAKF49A3D8M9S", "Check Out" },
                    { "01JZBSPCXBX6NRP69E2X96YSW5", "Bank Transfer In" },
                    { "01JZBSPK8EG8Z9AVT9K2KQ3XXC", "Bank Transfer Out" }
                });

            migrationBuilder.InsertData(
                table: "ScreensAccessProfileDetails",
                columns: new[] { "Id", "CanAdd", "CanDelete", "CanEdit", "CanOpen", "CanPrint", "CanShow", "ScreenId", "ScreensAccessProfileId" },
                values: new object[,]
                {
                    { "67FKPX6A0064R32JJP8H24ANT1", false, false, false, false, false, true, "01JVDDEWAV2Y5X4PHYM77X7N0K", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CC1P", false, false, false, false, false, true, "01JVDDF06EDYCFBKYBQ6HJA9B0", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CCT6", false, false, false, false, false, true, "01JVDDF3FVVBEPAZ4P6JW9A7VT", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CDSG", false, false, false, false, false, true, "01JVDDF701Z5JTMT806618B3F0", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CGAQ", false, false, false, false, false, true, "01JVDDFAWMWF4WM11MVFS4RE57", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CH9G", false, false, false, false, false, true, "01JVDDFE0NWTYZ240ACVMXEMDF", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CJ25", false, false, false, false, false, true, "01JVDDFHEBWM6CD8QSKDZFTK2W", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CKJT", false, false, false, false, false, true, "01JVDDFNZ5E91ZMTK93SVBZ7P4", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CMJS", false, false, false, false, false, true, "01JVDDFRY8QR4PRX21SC9J23WG", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CNSQ", false, false, false, false, false, true, "01JVDDFW74XJS7C2D605TNCD26", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24CPHH", false, false, false, false, false, true, "01JVDDFZ1YB6QRVPSPPYKNTRFK", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24ED1P", false, false, false, false, false, true, "01JVDDG46MYREZC2FR685SVJ0Q", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24EDSJ", false, false, false, false, false, true, "01JVDDG721H4X37Q0CKJDWVYDM", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24EEAA", false, false, false, false, false, true, "01JVDDG9JYD4KDHDRBP6226K36", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24EH2B", false, false, false, false, false, true, "01JVDDGDKH8N0DW4PVKW4640B0", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24EHTH", false, false, false, false, false, true, "01JVDDGGQ7TT2MWJVPXYYN8TTC", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24EJTJ", false, false, false, false, false, true, "01JVDDGKRWHXCBWFA4DG8N2V38", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GDHR", false, false, false, false, false, true, "01JVDDH685AAYHRWX94ZNRT9QB", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GGAQ", false, false, false, false, false, true, "01JVDDHAWK35FMS58RM8VVWWXZ", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GH23", false, false, false, false, false, true, "01JVDDHDCFYMFHJ9K9YAV6F81Z", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GJ2R", false, false, false, false, false, true, "01JVDDHHXK37EZSXTXV8EX8BF5", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GKA7", false, false, false, false, false, true, "01JVDDHMGK13EGC7FRY6Q42T8C", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GM9K", false, false, false, false, false, true, "01JVDDHQ3D35EKKWAFFZ85Z97Q", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GN1P", false, false, false, false, false, true, "01JVDDHT620EY2GA969M52PAXF", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24GP2H", false, false, false, false, false, true, "01JVDDHXQAK960655EDA6E8NKJ", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24MC9J", false, false, false, false, false, true, "01JVDDJ12X03EPSWZR4VJGD2XA", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24MD2J", false, false, false, false, false, true, "01JVDDJ4RQ2WASRZWEW0XKDG8Z", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24ME2S", false, false, false, false, false, true, "01JVDDJ8YMZWNS14YXFA6FTTMC", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24MGJK", false, false, false, false, false, true, "01JVDDJBSY2SXXEGTHSBVNTAZM", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8H24MHHH", false, false, false, false, false, true, "01JVDDJF1JSH5D1FB7JHPAY372", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8N95MDJQ", false, false, false, false, false, true, "01JVERZ6WQNV4MFHE749ZR5N8M", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0064R32JJP8N95MGJ7", false, false, false, false, false, true, "01JVERZBGWA4EGRR50MH56ZV78", "01JVDCZD2ST67FD5DFX2KECWKR" },
                    { "67FKPX6A0068R32JJP8H24ANT1", false, false, false, false, false, true, "01JVDDEWAV2Y5X4PHYM77X7N0K", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CC1P", false, false, false, false, false, true, "01JVDDF06EDYCFBKYBQ6HJA9B0", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CCT6", false, false, false, false, false, true, "01JVDDF3FVVBEPAZ4P6JW9A7VT", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CDSG", false, false, false, false, false, true, "01JVDDF701Z5JTMT806618B3F0", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CGAQ", false, false, false, false, false, true, "01JVDDFAWMWF4WM11MVFS4RE57", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CH9G", false, false, false, false, false, true, "01JVDDFE0NWTYZ240ACVMXEMDF", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CJ25", false, false, false, false, false, true, "01JVDDFHEBWM6CD8QSKDZFTK2W", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CKJT", false, false, false, false, false, true, "01JVDDFNZ5E91ZMTK93SVBZ7P4", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CMJS", false, false, false, false, false, true, "01JVDDFRY8QR4PRX21SC9J23WG", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CNSQ", false, false, false, false, false, true, "01JVDDFW74XJS7C2D605TNCD26", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24CPHH", false, false, false, false, false, true, "01JVDDFZ1YB6QRVPSPPYKNTRFK", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24ED1P", false, false, false, false, false, true, "01JVDDG46MYREZC2FR685SVJ0Q", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24EDSJ", false, false, false, false, false, true, "01JVDDG721H4X37Q0CKJDWVYDM", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24EEAA", false, false, false, false, false, true, "01JVDDG9JYD4KDHDRBP6226K36", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24EH2B", false, false, false, false, false, true, "01JVDDGDKH8N0DW4PVKW4640B0", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24EHTH", false, false, false, false, false, true, "01JVDDGGQ7TT2MWJVPXYYN8TTC", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24EJTJ", false, false, false, false, false, true, "01JVDDGKRWHXCBWFA4DG8N2V38", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GDHR", false, false, false, false, false, true, "01JVDDH685AAYHRWX94ZNRT9QB", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GGAQ", false, false, false, false, false, true, "01JVDDHAWK35FMS58RM8VVWWXZ", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GH23", false, false, false, false, false, true, "01JVDDHDCFYMFHJ9K9YAV6F81Z", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GJ2R", false, false, false, false, false, true, "01JVDDHHXK37EZSXTXV8EX8BF5", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GKA7", false, false, false, false, false, true, "01JVDDHMGK13EGC7FRY6Q42T8C", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GM9K", false, false, false, false, false, true, "01JVDDHQ3D35EKKWAFFZ85Z97Q", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GN1P", false, false, false, false, false, true, "01JVDDHT620EY2GA969M52PAXF", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24GP2H", false, false, false, false, false, true, "01JVDDHXQAK960655EDA6E8NKJ", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24MC9J", false, false, false, false, false, true, "01JVDDJ12X03EPSWZR4VJGD2XA", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24MD2J", false, false, false, false, false, true, "01JVDDJ4RQ2WASRZWEW0XKDG8Z", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24ME2S", false, false, false, false, false, true, "01JVDDJ8YMZWNS14YXFA6FTTMC", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24MGJK", false, false, false, false, false, true, "01JVDDJBSY2SXXEGTHSBVNTAZM", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8H24MHHH", false, false, false, false, false, true, "01JVDDJF1JSH5D1FB7JHPAY372", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8N95MDJQ", false, false, false, false, false, true, "01JVERZ6WQNV4MFHE749ZR5N8M", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" },
                    { "67FKPX6A0068R32JJP8N95MGJ7", false, false, false, false, false, true, "01JVERZBGWA4EGRR50MH56ZV78", "01JVDCZHG4FZWV2XFZ4EEYJN3Z" }
                });

            migrationBuilder.InsertData(
                table: "TaxSubType",
                columns: new[] { "Id", "Code", "Desc_ar", "Desc_en", "TaxTypeId" },
                values: new object[,]
                {
                    { "01JRN6KEMJ1YSCTA2H6T6SED3P", "V006", "إعفاءات الدفاع والأمن القومى", "Defence and National security Exemptions", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMJ3NA115CBHHXNJMNY", "V005", "إعفاءات دبلوماسين والقنصليات والسفارات", "Exemptions for diplomats, consulates and embassies", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMJDGQ75J7RBKNTSTQF", "V003", "سلعة أو خدمة معفاة", "Exempted good or service", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMJHZ2R4W9AXTZA6766", "V001", "تصدير للخارج", "Export", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMJN95XY34QFPWNEH0S", "V004", "سلعة أو خدمة غير خاضعة للضريبة", "A non-taxable good or service", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMJYW8C4QAVZH5MFST5", "V002", "تصدير مناطق حرة وأخرى", "Export to free areas and other areas", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMK16XJXNQC94B8P1P3", "W001", "المقاولات", "Contracting", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMK6V5HH0W4PNZRZTKV", "V009", "سلع عامة", "General Item sales", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMKBWWK3D8TX38V7ZPC", "W003", "المشتريات", "Purachases", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMKCS6EH5C8G1KT33FC", "Tbl02", "ضريبه الجدول (النوعية)", "Table tax (Fixed Amount)", "01JRN6KEMHQ1MEF9FBCDT2DA5S" },
                    { "01JRN6KEMKD21BYKY0WSHCVT4G", "W008", "جميعالخصومات والمنح والعمولات  التيتمنحها  شركات البترول والاتصالات ...وغيرها من الشركات المخاطبة بنظام الخصم", "Alldiscounts & grants & commissions granted by petroleum &telecommunications & other companies", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMKKTWSK3JFV867PDGR", "W004", "الخدمات", "Services", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMKMCDVD7FRD6BVANG4", "W005", "المبالغالتي تدفعها الجميعات التعاونية للنقل بالسيارات لاعضائها", "Sumspaid by the cooperative societies for car transportation to their members", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMKQRZFEB9ETRAP0VQZ", "V007", "إعفاءات اتفاقيات", "Agreements exemptions", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMKSXCH3YEM40NV096J", "V010", "نسب ضريبة أخرى", "Other Rates", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMKTA96NHMZAGVDY0E6", "Tbl01", "ضريبه الجدول (نسبيه)", "Table tax (percentage)", "01JRN6KEMHT2B8G7NZVX6J0B7W" },
                    { "01JRN6KEMKVBZ51PD3AN1PA1ZA", "W006", "الوكالةبالعمولة والسمسرة", "Commissionagency & brokerage", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMKVC7SSTYYN08KHRAP", "V008", "إعفاءات خاصة و أخرى", "Special Exemptios and other reasons", "01JRN6KEMGHX79ZW8H9GPDEF72" },
                    { "01JRN6KEMKWJB4PXVWBX0139BV", "W002", "التوريدات", "Supplies", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMKXYYQP6SKY6ZSA30E", "W007", "الخصوماتوالمنح والحوافز الاستثنائية ةالاضافية التي تمنحها شركات الدخان والاسمنت ", "Discounts& grants & additional exceptional incentives granted by smoke &cement companies", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMM3ABBYMMX9GWY5T60", "RD02", "رسم تنميه الموارد (قطعية)", "Resource development fee (amount)", "01JRN6KEMJ7CWC2EZBRBKD2RFJ" },
                    { "01JRN6KEMM43A145PBRYW0A8CH", "ST02", "ضريبه الدمغه (قطعيه بمقدار ثابت)", "Stamping Tax (amount)", "01JRN6KEMJ1FATNAQ3540BR73H" },
                    { "01JRN6KEMM5NDW2Q2BVR60JV3H", "W012", "تحصيل المستشفيات من الاطباء", "Hospitals collecting from doctors", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMM67M7BNBJ9FAPR43J", "RD01", "رسم تنميه الموارد (نسبة)", "Resource development fee (rate)", "01JRN6KEMJ7CWC2EZBRBKD2RFJ" },
                    { "01JRN6KEMM6Y65XSCXD951QW26", "Ent02", "ضريبة الملاهى (قطعية)", "Entertainment tax (amount)", "01JRN6KEMJCN4J6XCNJGBCSH9D" },
                    { "01JRN6KEMMA4AMVGMZQHMK19WF", "W016", "دفعات مقدمه", "advance payments", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMAP568B5A5CQQ78K4", "SC02", "رسم خدمة (قطعية)", "Service charges (amount)", "01JRN6KEMJHNG5Q0R5AMHSBV85" },
                    { "01JRN6KEMMD545W0VC5HPP6CMH", "W015", "أعفاء", "Exemption", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMFYB9TP9SARKH30HY", "SC01", "رسم خدمة (نسبة)", "Service charges (rate)", "01JRN6KEMJHNG5Q0R5AMHSBV85" },
                    { "01JRN6KEMMHDGXE64KP2PH6EE4", "W013", "الاتاوات", "Royalties", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMK5Y5624NNFY3CM2X", "W009", "مساندة دعم الصادرات التي يمنحها صندوق تنمية الصادرات ", "Supporting export subsidies", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMKPEKR0H72HVAXK3R", "Ent01", "ضريبة الملاهى (نسبة)", "Entertainment tax (rate)", "01JRN6KEMJCN4J6XCNJGBCSH9D" },
                    { "01JRN6KEMMPGBKG2JDHFYWS9FH", "W014", "تخليص جمركي ", "Customs clearance", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMQQANZQHGBAQZ74ZV", "W011", "العمولة والسمسرة _م_57", "Commission & brokerage _A_57", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMT81BZZFVSR758YVM", "W010", "اتعاب مهنية", "Professional fees", "01JRN6KEMHMNRPK8CTMAZ3XQTK" },
                    { "01JRN6KEMMYAWRRQTZG75XKZ68", "ST01", "ضريبه الدمغه (نسبيه)", "Stamping tax (percentage)", "01JRN6KEMHVHVT0JR971YVPJ92" },
                    { "01JRN6KEMNDB4GR4N4NMH112ES", "Mn01", "رسم المحليات (نسبة)", "Municipality Fees (rate)", "01JRN6KEMJ1EXMTWFQQ8BYP7RS" },
                    { "01JRN6R5RV78GJ7RVTYF1R71PD", "ST03", "ضريبه الدمغه (نسبيه)", "Stamping tax (percentage)", "01JRN6R5RWY935F174XQM19Y1T" },
                    { "01JRN6R5RVAZ79A391959E97ZY", "MI02", "رسم التامين الصحى (قطعية)", "Medical insurance fee (amount)", "01JRN6KEMJ36AZH3TSZM1QXXQ2" },
                    { "01JRN6R5RVF7YCQ9H08A9ATJYH", "Mn02", "رسم المحليات (قطعية)", "Municipality Fees (amount)", "01JRN6KEMJ1EXMTWFQQ8BYP7RS" },
                    { "01JRN6R5RVRMKR8A43X519YQ7Y", "OF02", "رسوم أخرى (قطعية)", "Other fees (amount)", "01JRN6KEMJ4X5C3NYGDDSZEGEM" },
                    { "01JRN6R5RVRQQGXHVN4RRAM98G", "MI01", "رسم التامين الصحى (نسبة)", "Medical insurance fee (rate)", "01JRN6KEMJ36AZH3TSZM1QXXQ2" },
                    { "01JRN6R5RVXDKMWNBE94H65ES3", "ST04", "ضريبه الدمغه (قطعيه بمقدار ثابت)", "Stamping Tax (amount)", "01JRN6R5RW3G72VRJ4Q2CA2JTV" },
                    { "01JRN6R5RVY6G76MCFE14P5F9X", "OF01", "رسوم أخرى (نسبة)", "Other fees (rate)", "01JRN6KEMJ4X5C3NYGDDSZEGEM" },
                    { "01JRN6R5RW16W2TCJTEXPB22FH", "OF04", "رسوم أخرى (قطعية)", "Other fees (amount)", "01JRN6R5RWNEB4PMWHKDY8KEEW" },
                    { "01JRN6R5RW1CAGGTV477HF59AD", "Mn03", "رسم المحليات (نسبة)", "Municipality Fees (rate)", "01JRN6R5RWB9NB0XFSY0G9VG4S" },
                    { "01JRN6R5RW49QW3MNMS8XG2T9M", "Mn04", "رسم المحليات (قطعية)", "Municipality Fees (amount)", "01JRN6R5RWB9NB0XFSY0G9VG4S" },
                    { "01JRN6R5RW4NEPJXPJT8TF4SV2", "SC04", "رسم خدمة (قطعية)", "Service charges (amount)", "01JRN6R5RWFYWMR74SJCX2G4XN" },
                    { "01JRN6R5RW4TE80A5E1DPND9SE", "MI04", "رسم التامين الصحى (قطعية)", "Medical insurance fee (amount)", "01JRN6R5RWET9R49NR7QTZA801" },
                    { "01JRN6R5RWGVAV02Q9T02G4CH6", "Ent04", "ضريبة الملاهى (قطعية)", "Entertainment tax (amount)", "01JRN6R5RWG7KPJMKBDAHZVHDH" },
                    { "01JRN6R5RWK548QE6RKDV8NKXZ", "OF03", "رسوم أخرى (نسبة)", "Other fees (rate)", "01JRN6R5RWNEB4PMWHKDY8KEEW" },
                    { "01JRN6R5RWKSP3T79AGWKFSJ4E", "MI03", "رسم التامين الصحى (نسبة)", "Medical insurance fee (rate)", "01JRN6R5RWET9R49NR7QTZA801" },
                    { "01JRN6R5RWR4NE3D53E1T1MYPS", "SC03", "رسم خدمة (نسبة)", "Service charges (rate)", "01JRN6R5RWFYWMR74SJCX2G4XN" },
                    { "01JRN6R5RWR5ZT5NTWW5CNCKTT", "Ent03", "ضريبة الملاهى (نسبة)", "Entertainment tax (rate)", "01JRN6R5RWG7KPJMKBDAHZVHDH" },
                    { "01JRN6R5RWRA4Q22AGM4CR283T", "RD04", "رسم تنميه الموارد (قطعية)", "Resource development fee (amount)", "01JRN6R5RW7PD5HBF3563V3JN7" },
                    { "01JRN6R5RWXJNBXBPHW42EKGPV", "RD03", "رسم تنميه الموارد (نسبة)", "Resource development fee (rate)", "01JRN6R5RW7PD5HBF3563V3JN7" }
                });

            migrationBuilder.InsertData(
                table: "User",
                columns: new[] { "Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EmployeeId", "IsActive", "ModifaiedAt", "ModifaiedBy", "Password", "ScreensAccessProfileId", "SettingId", "UserName", "UserTypeId" },
                values: new object[,]
                {
                    { "01JRK7KJVTERSKVHZGGX4DQ4M0", null, null, null, null, "01JRK7Q6NDYC4AEEPQBWNX7V2G", true, null, null, "123456", "01JVDCZD2ST67FD5DFX2KECWKR", "00000000000000000000000000", "Admin", "01JPBH5VCQFNHEEFRPAN170618" },
                    { "01JRK7KXPW5RY0FPRAPV06E9G9", null, null, null, null, "01JRK7QNHFMD61A648A9N09122", true, null, null, "123456", "01JVDCZHG4FZWV2XFZ4EEYJN3Z", "00000000000000000000000000", "User", "01JPBH694D11N4XRP18J9HWQWX" }
                });

            migrationBuilder.InsertData(
                table: "Employee",
                columns: new[] { "Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EmployeeEMail", "EmployeeIsRep", "EmployeeName", "EmployeePhone", "IsActive", "ModifaiedAt", "ModifaiedBy", "UserId" },
                values: new object[,]
                {
                    { "01JRK7Q6NDYC4AEEPQBWNX7V2G", null, null, null, null, "<EMAIL>", false, "Admin", "1234567890", true, null, null, "01JRK7KJVTERSKVHZGGX4DQ4M0" },
                    { "01JRK7QNHFMD61A648A9N09122", null, null, null, null, "<EMAIL>", false, "User", "1234567890", true, null, null, "01JRK7KXPW5RY0FPRAPV06E9G9" }
                });

            migrationBuilder.InsertData(
                table: "Setting",
                columns: new[] { "Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "IsActive", "ModifaiedAt", "ModifaiedBy", "UserId" },
                values: new object[] { "01JM898XB2RH67PPFANBQV0KRG", null, null, null, null, true, null, null, "01JRK7KJVTERSKVHZGGX4DQ4M0" });

            migrationBuilder.CreateIndex(
                name: "IX_BankAccount_BankId",
                table: "BankAccount",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_BankAccountId",
                table: "BankTransferTreasuryVoucher",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_BankId",
                table: "BankTransferTreasuryVoucher",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_BeneficiaryTypeId",
                table: "BankTransferTreasuryVoucher",
                column: "BeneficiaryTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_CustomerId",
                table: "BankTransferTreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_EmployeeId",
                table: "BankTransferTreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_TransactionTypeId",
                table: "BankTransferTreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_VendorId",
                table: "BankTransferTreasuryVoucher",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_Bill_PaymentTermId",
                table: "Bill",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_Bill_VendorId",
                table: "Bill",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_Bill_VendorTypeId",
                table: "Bill",
                column: "VendorTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BillReturn_PaymentTermId",
                table: "BillReturn",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_BillReturn_VendorId",
                table: "BillReturn",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_BillReturn_VendorTypeId",
                table: "BillReturn",
                column: "VendorTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_BeneficiaryTypeId",
                table: "CashTreasuryVoucher",
                column: "BeneficiaryTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_CustomerId",
                table: "CashTreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_DrawerId",
                table: "CashTreasuryVoucher",
                column: "DrawerId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_DrawerLocationId",
                table: "CashTreasuryVoucher",
                column: "DrawerLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_EmployeeId",
                table: "CashTreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_TransactionTypeId",
                table: "CashTreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_VendorId",
                table: "CashTreasuryVoucher",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckDeposit_BankAccountId",
                table: "CheckDeposit",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckDeposit_BankId",
                table: "CheckDeposit",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReturn_CheckVaultId",
                table: "CheckReturn",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReturn_CheckVaultLocationId",
                table: "CheckReturn",
                column: "CheckVaultLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckClearId",
                table: "CheckStatusHistory",
                column: "CheckClearId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckCollectionId",
                table: "CheckStatusHistory",
                column: "CheckCollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckDepositId",
                table: "CheckStatusHistory",
                column: "CheckDepositId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckRejectId",
                table: "CheckStatusHistory",
                column: "CheckRejectId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckReturnId",
                table: "CheckStatusHistory",
                column: "CheckReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckStatusFromId",
                table: "CheckStatusHistory",
                column: "CheckStatusFromId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckStatusToId",
                table: "CheckStatusHistory",
                column: "CheckStatusToId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckTreasuryVoucherId",
                table: "CheckStatusHistory",
                column: "CheckTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_BankAccountId",
                table: "CheckTreasuryVoucher",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_BankId",
                table: "CheckTreasuryVoucher",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_BeneficiaryTypeId",
                table: "CheckTreasuryVoucher",
                column: "BeneficiaryTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckClearId",
                table: "CheckTreasuryVoucher",
                column: "CheckClearId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckCollectionId",
                table: "CheckTreasuryVoucher",
                column: "CheckCollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckDepositId",
                table: "CheckTreasuryVoucher",
                column: "CheckDepositId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckRejectId",
                table: "CheckTreasuryVoucher",
                column: "CheckRejectId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckReturnId",
                table: "CheckTreasuryVoucher",
                column: "CheckReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckStatusId",
                table: "CheckTreasuryVoucher",
                column: "CheckStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckVaultId",
                table: "CheckTreasuryVoucher",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckVaultLocationId",
                table: "CheckTreasuryVoucher",
                column: "CheckVaultLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CustomerId",
                table: "CheckTreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_EmployeeId",
                table: "CheckTreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_TransactionTypeId",
                table: "CheckTreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_VendorId",
                table: "CheckTreasuryVoucher",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckVaultLocation_CheckVaultId",
                table: "CheckVaultLocation",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_Customer_CustomerRepId",
                table: "Customer",
                column: "CustomerRepId");

            migrationBuilder.CreateIndex(
                name: "IX_Customer_CustomerTypeId",
                table: "Customer",
                column: "CustomerTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Customer_PaymentTermId",
                table: "Customer",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_DrawerLocation_DrawerId",
                table: "DrawerLocation",
                column: "DrawerId");

            migrationBuilder.CreateIndex(
                name: "IX_Employee_UserId",
                table: "Employee",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_BillId",
                table: "Inventory",
                column: "BillId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_BillReturnId",
                table: "Inventory",
                column: "BillReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_InvoiceId",
                table: "Inventory",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_InvoiceReturnId",
                table: "Inventory",
                column: "InvoiceReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_ProductId",
                table: "Inventory",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_ProductUnitId",
                table: "Inventory",
                column: "ProductUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_PurchaseOrderId",
                table: "Inventory",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_PurchaseOrderLineId",
                table: "Inventory",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_SalesOrderId",
                table: "Inventory",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_SalesOrderLineId",
                table: "Inventory",
                column: "SalesOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_StoreId",
                table: "Inventory",
                column: "StoreId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_TransactionTypeId",
                table: "Inventory",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryProductTax_InventoryId",
                table: "InventoryProductTax",
                column: "InventoryId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryProductTax_TaxSubTypeId",
                table: "InventoryProductTax",
                column: "TaxSubTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryProductTax_TaxTypeId",
                table: "InventoryProductTax",
                column: "TaxTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoice_CustomerId",
                table: "Invoice",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoice_CustomerRepId",
                table: "Invoice",
                column: "CustomerRepId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoice_CustomerTypeId",
                table: "Invoice",
                column: "CustomerTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoice_PaymentTermId",
                table: "Invoice",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReturn_CustomerId",
                table: "InvoiceReturn",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReturn_CustomerRepId",
                table: "InvoiceReturn",
                column: "CustomerRepId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReturn_CustomerTypeId",
                table: "InvoiceReturn",
                column: "CustomerTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReturn_PaymentTermId",
                table: "InvoiceReturn",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_Product_ProductCategoryId",
                table: "Product",
                column: "ProductCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Product_ProductTypeId",
                table: "Product",
                column: "ProductTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Product_UnitModelId",
                table: "Product",
                column: "UnitModelId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductTax_ProductId",
                table: "ProductTax",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductTax_TaxSubTypeId",
                table: "ProductTax",
                column: "TaxSubTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductTax_TaxTypeId",
                table: "ProductTax",
                column: "TaxTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductUnit_ProductId",
                table: "ProductUnit",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductUnit_ProductUnitId",
                table: "ProductUnit",
                column: "ProductUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_PaymentTermId",
                table: "PurchaseOrder",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_VendorId",
                table: "PurchaseOrder",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_VendorTypeId",
                table: "PurchaseOrder",
                column: "VendorTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLine_ProductId",
                table: "PurchaseOrderLine",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLine_ProductUnitId",
                table: "PurchaseOrderLine",
                column: "ProductUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLine_PurchaseOrderId",
                table: "PurchaseOrderLine",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_RefreshTokens_UserId",
                table: "RefreshTokens",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CustomerId",
                table: "SalesOrder",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CustomerRepId",
                table: "SalesOrder",
                column: "CustomerRepId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CustomerTypeId",
                table: "SalesOrder",
                column: "CustomerTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_PaymentTermId",
                table: "SalesOrder",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrderLine_ProductId",
                table: "SalesOrderLine",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrderLine_ProductUnitId",
                table: "SalesOrderLine",
                column: "ProductUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrderLine_SalesOrderId",
                table: "SalesOrderLine",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_ScreensAccessProfile_UserModelId",
                table: "ScreensAccessProfile",
                column: "UserModelId");

            migrationBuilder.CreateIndex(
                name: "IX_ScreensAccessProfileDetails_ScreensAccessProfileId",
                table: "ScreensAccessProfileDetails",
                column: "ScreensAccessProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_Setting_UserId",
                table: "Setting",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxSubType_TaxTypeId",
                table: "TaxSubType",
                column: "TaxTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_BankTransferTreasuryVoucherId",
                table: "TreasuryLine",
                column: "BankTransferTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_BillId",
                table: "TreasuryLine",
                column: "BillId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_BillReturnId",
                table: "TreasuryLine",
                column: "BillReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_CashTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CashTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_CheckTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CheckTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_ExpensesId",
                table: "TreasuryLine",
                column: "ExpensesId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_InvoiceId",
                table: "TreasuryLine",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_InvoiceReturnId",
                table: "TreasuryLine",
                column: "InvoiceReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_User_ScreensAccessProfileId",
                table: "User",
                column: "ScreensAccessProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_Vendor_PaymentTermId",
                table: "Vendor",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_Vendor_VendorTypeId",
                table: "Vendor",
                column: "VendorTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_BankTransferTreasuryVoucher_Customer_CustomerId",
                table: "BankTransferTreasuryVoucher",
                column: "CustomerId",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BankTransferTreasuryVoucher_Employee_EmployeeId",
                table: "BankTransferTreasuryVoucher",
                column: "EmployeeId",
                principalTable: "Employee",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CashTreasuryVoucher_Customer_CustomerId",
                table: "CashTreasuryVoucher",
                column: "CustomerId",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CashTreasuryVoucher_Employee_EmployeeId",
                table: "CashTreasuryVoucher",
                column: "EmployeeId",
                principalTable: "Employee",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckStatusHistory_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckStatusHistory",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_Customer_CustomerId",
                table: "CheckTreasuryVoucher",
                column: "CustomerId",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_Employee_EmployeeId",
                table: "CheckTreasuryVoucher",
                column: "EmployeeId",
                principalTable: "Employee",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Employee_CustomerRepId",
                table: "Customer",
                column: "CustomerRepId",
                principalTable: "Employee",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Employee_User_UserId",
                table: "Employee",
                column: "UserId",
                principalTable: "User",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RefreshTokens_User_UserId",
                table: "RefreshTokens",
                column: "UserId",
                principalTable: "User",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ScreensAccessProfile_User_UserModelId",
                table: "ScreensAccessProfile",
                column: "UserModelId",
                principalTable: "User",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ScreensAccessProfile_User_UserModelId",
                table: "ScreensAccessProfile");

            migrationBuilder.DropTable(
                name: "Audit");

            migrationBuilder.DropTable(
                name: "CheckStatusHistory");

            migrationBuilder.DropTable(
                name: "InventoryProductTax");

            migrationBuilder.DropTable(
                name: "ProductTax");

            migrationBuilder.DropTable(
                name: "ProductUnit");

            migrationBuilder.DropTable(
                name: "RefreshTokens");

            migrationBuilder.DropTable(
                name: "ScreensAccessProfileDetails");

            migrationBuilder.DropTable(
                name: "Setting");

            migrationBuilder.DropTable(
                name: "TreasuryLine");

            migrationBuilder.DropTable(
                name: "Inventory");

            migrationBuilder.DropTable(
                name: "TaxSubType");

            migrationBuilder.DropTable(
                name: "BankTransferTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "CashTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "CheckTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "Expenses");

            migrationBuilder.DropTable(
                name: "BillReturn");

            migrationBuilder.DropTable(
                name: "Bill");

            migrationBuilder.DropTable(
                name: "InvoiceReturn");

            migrationBuilder.DropTable(
                name: "Invoice");

            migrationBuilder.DropTable(
                name: "PurchaseOrderLine");

            migrationBuilder.DropTable(
                name: "SalesOrderLine");

            migrationBuilder.DropTable(
                name: "Store");

            migrationBuilder.DropTable(
                name: "TaxType");

            migrationBuilder.DropTable(
                name: "DrawerLocation");

            migrationBuilder.DropTable(
                name: "BeneficiaryType");

            migrationBuilder.DropTable(
                name: "CheckClear");

            migrationBuilder.DropTable(
                name: "CheckCollection");

            migrationBuilder.DropTable(
                name: "CheckDeposit");

            migrationBuilder.DropTable(
                name: "CheckReject");

            migrationBuilder.DropTable(
                name: "CheckReturn");

            migrationBuilder.DropTable(
                name: "CheckStatus");

            migrationBuilder.DropTable(
                name: "TransactionType");

            migrationBuilder.DropTable(
                name: "PurchaseOrder");

            migrationBuilder.DropTable(
                name: "Product");

            migrationBuilder.DropTable(
                name: "SalesOrder");

            migrationBuilder.DropTable(
                name: "Drawer");

            migrationBuilder.DropTable(
                name: "BankAccount");

            migrationBuilder.DropTable(
                name: "CheckVaultLocation");

            migrationBuilder.DropTable(
                name: "Vendor");

            migrationBuilder.DropTable(
                name: "ProductCategory");

            migrationBuilder.DropTable(
                name: "ProductType");

            migrationBuilder.DropTable(
                name: "Unit");

            migrationBuilder.DropTable(
                name: "Customer");

            migrationBuilder.DropTable(
                name: "Bank");

            migrationBuilder.DropTable(
                name: "CheckVault");

            migrationBuilder.DropTable(
                name: "VendorType");

            migrationBuilder.DropTable(
                name: "CustomerType");

            migrationBuilder.DropTable(
                name: "Employee");

            migrationBuilder.DropTable(
                name: "PaymentTerm");

            migrationBuilder.DropTable(
                name: "User");

            migrationBuilder.DropTable(
                name: "ScreensAccessProfile");
        }
    }
}
