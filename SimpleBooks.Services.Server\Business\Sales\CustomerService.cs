﻿namespace SimpleBooks.Services.Server.Business.Sales
{
    public class CustomerService : SimpleBooksBaseService<CustomerModel, IndexCustomerViewModel, CreateCustomerViewModel, UpdateCustomerViewModel>, ICustomerService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICustomerTypeService _customerTypeService;
        private readonly IEmployeeService _employeeService;
        private readonly IPaymentTermService _paymentTermService;

        public CustomerService(
            IAuthenticationValidationService authenticationValidationService,
            IUnitOfWork unitOfWork,
            ICustomerTypeService customerTypeService,
            IEmployeeService employeeService,
            IPaymentTermService paymentTermService) : base(authenticationValidationService, unitOfWork.Customer)
        {
            _unitOfWork = unitOfWork;
            _customerTypeService = customerTypeService;
            _employeeService = employeeService;
            _paymentTermService = paymentTermService;
        }

        public override void EditModelBeforeSave(CustomerModel model)
        {
            base.EditModelBeforeSave(model);

            if (model.CustomerTypeId == Ulid.Empty)
                model.CustomerTypeId = null;
            if (model.CustomerRepId == Ulid.Empty)
                model.CustomerRepId = null;
            if (model.PaymentTermId == Ulid.Empty)
                model.PaymentTermId = null;
        }

        public override void ValidateEntity(CustomerModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<CustomerModel> repositorySpecifications = new RepositorySpecifications<CustomerModel>()
                {
                    SearchValue = x => x.CustomerName == model.CustomerName || x.CustomerTaxCardNumber == model.CustomerTaxCardNumber,
                    IsTackable = false,
                };
                var isExistingCustomer = _unitOfWork.Customer.Get(repositorySpecifications);
                if (isExistingCustomer != null && isExistingCustomer.Id != model.Id)
                    throw new ValidationException("Customer with the same Name or Tax Card Number already exists.");
            }
        }

        public async Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerDtoListAsync()
        {
            return (await _unitOfWork.Customer.GetAllAsync()).Select(x => new CustomerDto()
            {
                Id = x.Id,
                CustomerName = x.CustomerName,
                CustomerTaxCardNumber = x.CustomerTaxCardNumber,
                CustomerTypeId = x.CustomerTypeId,
                CustomerRepId = x.CustomerRepId,
                PaymentTermId = x.PaymentTermId,
            }).ToList();
        }
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync() => await _customerTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync() => await _employeeService.GetRepEmployeeSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectivePaymentTermListAsync() => await _paymentTermService.GetSelectListAsync();
    }
}
