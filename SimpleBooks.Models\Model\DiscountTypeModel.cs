namespace SimpleBooks.Models.Model
{
    [Table("DiscountType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<DiscountTypeModel>))]
    public class DiscountTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Discount Type Name")]
        public string DiscountTypeName { get; set; }

        // Navigation properties for line-level discounts
        public virtual ICollection<SalesOrderLineModel> SalesOrderLines { get; set; } = new List<SalesOrderLineModel>();
        public virtual ICollection<PurchaseOrderLineModel> PurchaseOrderLines { get; set; } = new List<PurchaseOrderLineModel>();
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();

        // Navigation properties for document-level discounts
        public virtual ICollection<SalesOrderModel> SalesOrdersDocumentDiscount { get; set; } = new List<SalesOrderModel>();
        public virtual ICollection<PurchaseOrderModel> PurchaseOrdersDocumentDiscount { get; set; } = new List<PurchaseOrderModel>();
        public virtual ICollection<InvoiceModel> InvoicesDocumentDiscount { get; set; } = new List<InvoiceModel>();
        public virtual ICollection<InvoiceReturnModel> InvoiceReturnsDocumentDiscount { get; set; } = new List<InvoiceReturnModel>();
        public virtual ICollection<BillModel> BillsDocumentDiscount { get; set; } = new List<BillModel>();
        public virtual ICollection<BillReturnModel> BillReturnsDocumentDiscount { get; set; } = new List<BillReturnModel>();
    }
}
