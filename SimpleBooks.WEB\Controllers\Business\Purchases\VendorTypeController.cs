﻿namespace SimpleBooks.WEB.Controllers.Business.Purchases
{
    public class VendorTypeController : BaseBusinessController<
        VendorTypeModel,
        VendorTypeModel,
        CreateVendorTypeViewModel,
        UpdateVendorTypeViewModel,
        IndexVendorTypeFormViewModel,
        CreateVendorTypeFormViewModel,
        UpdateVendorTypeFormViewModel>
    {
        private readonly IVendorTypeService _vendorTypeService;

        public VendorTypeController(IVendorTypeService vendorTypeService) : base(vendorTypeService)
        {
            _vendorTypeService = vendorTypeService;
        }

        public override async Task<IActionResult> Create(CreateVendorTypeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _vendorTypeService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("vendorType", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            VendorTypeModel? entity = await _vendorTypeService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateVendorTypeFormViewModel viewModel = new UpdateVendorTypeFormViewModel()
            {
                Id = entity.Id,
                VendorTypeName = entity.VendorTypeName,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateVendorTypeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _vendorTypeService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("vendorType", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateVendorTypeViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateVendorTypeViewModel> createEntites = new List<CreateVendorTypeViewModel>();
            var createEntitesDict = new Dictionary<string, CreateVendorTypeViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateVendorTypeViewModel
                    {
                        VendorTypeName = row.Cell(1).GetString(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateVendorTypeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateVendorTypeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
