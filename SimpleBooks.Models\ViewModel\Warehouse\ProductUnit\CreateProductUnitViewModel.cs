﻿namespace SimpleBooks.Models.ViewModel.Warehouse.ProductUnit
{
    public class CreateProductUnitViewModel : BaseCreateViewModel, IEntityMapper<ProductUnitModel, CreateProductUnitViewModel>
    {
        [DisplayName("Product Unit Sales Price")]
        public decimal ProductUnitSalesPrice { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit Ratio")]
        public decimal ProductUnitRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }

        public CreateProductUnitViewModel ToDto(ProductUnitModel entity) => entity.ToCreateDto();

        public ProductUnitModel ToEntity() => ProductUnitMapper.ToEntity(this);
    }
}
