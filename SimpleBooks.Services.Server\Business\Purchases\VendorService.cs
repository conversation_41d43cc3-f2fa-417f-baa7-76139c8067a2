﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class VendorService : SimpleBooksBaseService<VendorModel, IndexVendorViewModel, CreateVendorViewModel, UpdateVendorViewModel>, IVendorService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;

        public VendorService(
            IAuthenticationValidationService authenticationValidationService,
            IUnitOfWork unitOfWork,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService) : base(authenticationValidationService, unitOfWork.Vendor)
        {
            _unitOfWork = unitOfWork;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
        }

        public override void EditModelBeforeSave(VendorModel model)
        {
            base.EditModelBeforeSave(model);

            if (model.VendorTypeId == Ulid.Empty)
                model.VendorTypeId = null;
            if (model.PaymentTermId == Ulid.Empty)
                model.PaymentTermId = null;
        }

        public override void ValidateEntity(VendorModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<VendorModel> repositorySpecifications = new RepositorySpecifications<VendorModel>()
                {
                    SearchValue = x => x.VendorName == model.VendorName || x.VendorTaxCardNumber == model.VendorTaxCardNumber,
                    IsTackable = false,
                };
                var isExistingVendor = _unitOfWork.Vendor.Get(repositorySpecifications);
                if (isExistingVendor != null && isExistingVendor.Id != model.Id)
                    throw new ValidationException("Vendor with the same Name or Tax Card Number already exists.");
            }
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorDtoListAsync()
        {
            return (await _unitOfWork.Vendor.GetAllAsync()).Select(x => new VendorDto()
            {
                Id = x.Id,
                VendorName = x.VendorName,
                VendorTaxCardNumber = x.VendorTaxCardNumber,
                VendorTypeId = x.VendorTypeId,
                PaymentTermId = x.PaymentTermId,
            }).ToList();
        }
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectivePaymentTermListAsync() => await _paymentTermService.GetSelectListAsync();
    }
}
