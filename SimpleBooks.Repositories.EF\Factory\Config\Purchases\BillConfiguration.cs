﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Purchases
{
    public class BillConfiguration : IEntityTypeConfiguration<BillModel>
    {
        public void Configure(EntityTypeBuilder<BillModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.BillId).IsUnique();

            builder.HasOne(d => d.Vendor).WithMany(p => p.Bills)
                .HasForeignKey(d => d.VendorId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.VendorType).WithMany(p => p.Bills)
                .HasForeignKey(d => d.VendorTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.Bills)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
