﻿namespace SimpleBooks.Services.Server.Business.Sales
{
    public class CustomerTypeService : SimpleBooksBaseService<CustomerTypeModel, CustomerTypeModel, CreateCustomerTypeViewModel, UpdateCustomerTypeViewModel>, ICustomerTypeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CustomerTypeService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.CustomerType)
        {
            _unitOfWork = unitOfWork;
        }

        public override void ValidateEntity(CustomerTypeModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<CustomerTypeModel> repositorySpecifications = new RepositorySpecifications<CustomerTypeModel>()
                {
                    SearchValue = x => x.CustomerTypeName == model.CustomerTypeName,
                    IsTackable = false,
                };
                var isExistingCustomerType = _unitOfWork.CustomerType.Get(repositorySpecifications);
                if (isExistingCustomerType != null && isExistingCustomerType.Id != model.Id)
                    throw new ValidationException("Customer Type with the same Name already exists.");
            }
        }
    }
}
