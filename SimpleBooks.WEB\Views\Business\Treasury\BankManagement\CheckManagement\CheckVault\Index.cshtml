﻿@model IndexCheckVaultFormViewModel

@{
    ViewData["Title"] = "Check Vaults";
    List<CheckVaultModel> CheckVaults = Model.MainList.Items;
}

@if (TempData.Get<ImportResult>("ImportStatus") is ImportResult importResult)
    @await Html.PartialAsync("_ImportStatus", importResult)

<div class="row d-flex">
    <div class="me-3 pb-2">
        <a class="btn btn-secondary btn-lg float-start" asp-action="Create" method="get">
            <i class="bi bi-plus-circle-dotted"></i>
            Add Check Vault
        </a>
        <a class="btn btn-success btn-lg float-end" asp-action="ExportToExcel">
            <i class="bi bi-file-earmark-spreadsheet"></i>
            Export To Excel
        </a>
        <form asp-action="ImportFromExcel" enctype="multipart/form-data" method="post" id="excelUploadForm" style="display: none;">
            <input type="file" name="file" id="excelFileInput" accept=".xlsx, .xls" />
        </form>

        <a class="btn btn-success btn-lg float-end" href="#" onclick="document.getElementById('excelFileInput').click(); return false;">
            <i class="bi bi-file-earmark-spreadsheet-fill"></i>
            Import From Excel
        </a>

        <!-- Loading overlay -->
        <div id="loadingOverlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.7); z-index:9999; text-align:center; padding-top:20%;">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Importing...</span>
            </div>
            <p class="mt-2 fw-bold">Importing Excel file, please wait...</p>
        </div>
    </div>
    <form class="d-flex align-items-center" asp-action="Index">
        <input class="form-control me-3" type="search" asp-for="SearchValue" placeholder="Search">
        <button class="btn btn-info btn-lg" type="submit">Search</button>
    </form>
</div>

@if (!CheckVaults.Any())
{
    <div class="alert alert-warning mt-5">
        <h4 class="alert-heading">No Check Vaults!</h4>
        <p class="mb-0">No Check Vaults were added yet.</p>
    </div>
}
else
{
    <table class="table table-hover">
        <thead>
            <tr>
                <th scope="col">CheckVault Name</th>
                <th scope="col" class="">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (CheckVaultModel CheckVault in CheckVaults)
            {
                <tr>
                    <td>@CheckVault.CheckVaultName</td>

                    <td class="overflow-hidden align-middle">
                        <div class="d-flex justify-content-end">
                            <a class="btn btn-info rounded rounded-3 me-2" asp-action="Update" asp-route-id="@CheckVault.Id">
                                <i class="bi bi-pencil-fill"></i>
                            </a>
                            <a href="javascript:;" class="btn btn-danger rounded rounded-3 js-delete" data-id="@CheckVault.Id">
                                <i class="bi bi-trash3"></i>
                            </a>
                        </div>
                    </td>

                </tr>
            }
        </tbody>
    </table>

    if (Model != null && Model.MainList != null)
        @await Html.PartialAsync("_Pagination", Model)
}

@section Scripts
{
    <script src="~/js/CheckVault-index.js" asp-append-version="true"></script>
    <script>
        const fileInput = document.getElementById('excelFileInput');
        const form = document.getElementById('excelUploadForm');
        const overlay = document.getElementById('loadingOverlay');

        fileInput.addEventListener('change', function () {
            if (this.files.length > 0) {
                overlay.style.display = 'block'; // Show loading
                form.submit(); // Submit form
            }
        });
    </script>
}