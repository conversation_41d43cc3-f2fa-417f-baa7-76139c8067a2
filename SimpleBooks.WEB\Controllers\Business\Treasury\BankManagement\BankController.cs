﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement
{
    public class BankController : BaseBusinessController<
        BankModel,
        BankModel,
        CreateBankViewModel,
        UpdateBankViewModel,
        IndexBankFormViewModel,
        CreateBankFormViewModel,
        UpdateBankFormViewModel>
    {
        private readonly IBankService _bankService;

        public BankController(IBankService bankService) : base(bankService)
        {
            _bankService = bankService;
        }

        public override async Task<IActionResult> Create(CreateBankFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _bankService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("bank", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            BankModel? entity = await _bankService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateBankFormViewModel viewModel = new UpdateBankFormViewModel()
            {
                Id = entity.Id,
                BankName = entity.BankName,
                BankIdentifier = entity.BankIdentifier,
                BankAccounts = entity.BankAccounts.Select(x => x.ToUpdateDto()).ToList(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateBankFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _bankService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("bank", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateBankViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateBankViewModel> createEntites = new List<CreateBankViewModel>();
            var createEntitesDict = new Dictionary<string, CreateBankViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateBankViewModel
                    {
                        BankName = mainKey,
                        BankIdentifier = row.Cell(2).GetString(),
                        BankAccounts = new List<CreateBankAccountViewModel>(),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }

                var accountNumber = row.Cell(3).GetString();
                if (!string.IsNullOrEmpty(accountNumber))
                {
                    existingKey.BankAccounts.Add(new CreateBankAccountViewModel
                    {
                        BankAccountNumber = accountNumber,
                        BankAccountCurrency = row.Cell(4).GetString(),
                        BankAccountIBAN = row.Cell(5).GetString(),
                        BankAccountSwiftCode = row.Cell(6).GetString(),
                    });
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateBankFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateBankFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
