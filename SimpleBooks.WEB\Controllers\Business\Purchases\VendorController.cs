﻿namespace SimpleBooks.WEB.Controllers.Business.Purchases
{
    public class VendorController : BaseBusinessController<
        VendorModel,
        IndexVendorViewModel,
        CreateVendorViewModel,
        UpdateVendorViewModel,
        IndexVendorFormViewModel,
        CreateVendorFormViewModel,
        UpdateVendorFormViewModel>
    {
        private readonly IVendorService _vendorService;

        public VendorController(IVendorService vendorService) : base(vendorService)
        {
            _vendorService = vendorService;
        }

        public override async Task<IActionResult> Create(CreateVendorFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _vendorService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("vendor", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            VendorModel? entity = await _vendorService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateVendorFormViewModel viewModel = new UpdateVendorFormViewModel()
            {
                Id = entity.Id,
                VendorName = entity.VendorName,
                VendorTaxCardNumber = entity.VendorTaxCardNumber,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                VendorTypes = await _vendorService.SelectiveVendorTypeListAsync().ToSelectListItemAsync(),
                PaymentTerms = await _vendorService.SelectivePaymentTermListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateVendorFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _vendorService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("vendor", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateVendorViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateVendorViewModel> createEntites = new List<CreateVendorViewModel>();
            var createEntitesDict = new Dictionary<string, CreateVendorViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateVendorViewModel
                    {
                        VendorName = row.Cell(1).GetString(),
                        VendorTaxCardNumber = row.Cell(2).GetString(),
                        VendorTypeId = ValidateValue.ValidateUlid(row.Cell(3).GetString()),
                        PaymentTermId = ValidateValue.ValidateUlid(row.Cell(4).GetString()),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateVendorFormViewModel model)
        {
            model.VendorTypes = await _vendorService.SelectiveVendorTypeListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _vendorService.SelectivePaymentTermListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateVendorFormViewModel model)
        {
            model.VendorTypes = await _vendorService.SelectiveVendorTypeListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _vendorService.SelectivePaymentTermListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
