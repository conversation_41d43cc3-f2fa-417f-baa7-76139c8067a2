﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Purchases
{
    public class BillReturnConfiguration : IEntityTypeConfiguration<BillReturnModel>
    {
        public void Configure(EntityTypeBuilder<BillReturnModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.BillReturnId).IsUnique();

            builder.HasOne(d => d.Vendor).WithMany(p => p.BillReturns)
                .HasForeignKey(d => d.VendorId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.VendorType).WithMany(p => p.BillReturns)
                .HasForeignKey(d => d.VendorTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.BillReturns)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
