﻿namespace SimpleBooks.WEB.Controllers.Business.HR
{
    public class EmployeeController : BaseBusinessController<
        EmployeeModel,
        EmployeeModel,
        CreateEmployeeViewModel,
        UpdateEmployeeViewModel,
        IndexEmployeeFormViewModel,
        CreateEmployeeFormViewModel,
        UpdateEmployeeFormViewModel>
    {
        private readonly IEmployeeService _employeeService;

        public EmployeeController(IEmployeeService employeeService) : base(employeeService)
        {
            _employeeService = employeeService;
        }

        public override async Task<IActionResult> Create(CreateEmployeeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _employeeService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("employee", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            EmployeeModel? entity = await _employeeService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateEmployeeFormViewModel viewModel = new UpdateEmployeeFormViewModel()
            {
                Id = entity.Id,
                EmployeeName = entity.EmployeeName,
                EmployeeEMail = entity.EmployeeEMail,
                EmployeePhone = entity.EmployeePhone,
                EmployeeIsRep = entity.EmployeeIsRep,
                UserId = entity.UserId,
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateEmployeeFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _employeeService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("employee", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<List<CreateEmployeeViewModel>> HandleImportingEntity(IEnumerable<IXLRangeRow> rows)
        {
            List<CreateEmployeeViewModel> createEntites = new List<CreateEmployeeViewModel>();
            var createEntitesDict = new Dictionary<string, CreateEmployeeViewModel>();

            foreach (var row in rows)
            {
                var mainKey = row.Cell(1).GetString();

                if (!createEntitesDict.TryGetValue(mainKey, out var existingKey))
                {
                    existingKey = new CreateEmployeeViewModel
                    {
                        EmployeeName = row.Cell(1).GetString(),
                        EmployeeEMail = row.Cell(2).GetString(),
                        EmployeePhone = row.Cell(3).GetString(),
                        EmployeeIsRep = row.Cell(4).GetBoolean(),
                        UserId = ValidateValue.ValidateUlid(row.Cell(5).GetString()),
                    };

                    createEntitesDict[mainKey] = existingKey;
                }
            }

            createEntites = createEntitesDict.Values.ToList();
            return await Task.FromResult(createEntites);
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateEmployeeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateEmployeeFormViewModel model)
        {
            return await Task.FromResult(View(model));
        }
    }
}
